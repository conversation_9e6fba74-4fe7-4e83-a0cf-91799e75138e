<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Fresh Picks Online Grocery Store</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
</head>
<body>
    <!-- Header section with logo and navigation -->
    <header class="header">
        <a href="index.html" class="logo">
            <i class="fa fa-shopping-basket"></i> Fresh Picks
        </a>
        
        <!-- Search form -->
        <div class="search-form">
            <form>
                <input type="search" id="search-box" placeholder="Search products...">
                <button type="submit"><i class="fa fa-search"></i></button>
            </form>
            <div class="search-results" id="search-results"></div>
        </div>
        
        <!-- Shopping cart icon with item count -->
        <div class="icons">
            <div id="cart-btn" class="fa fa-shopping-cart"><span class="cart-count">0</span></div>
            <div id="menu-btn" class="fa fa-bars"></div>
        </div>
        
        <!-- Navigation menu -->
        <nav class="navbar">
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="products.html">Products</a></li>
                <li><a href="about.html">About Us</a></li>
                <li><a href="contact.html" class="active">Contact Us</a></li>
            </ul>
        </nav>
    </header>
    
    <!-- Shopping cart sidebar -->
    <div class="cart-sidebar">
        <div class="cart-header">
            <h3>Your Cart</h3>
            <div id="close-cart" class="fa fa-times"></div>
        </div>
        <div class="cart-items">
            <!-- Cart items will be dynamically added here -->
        </div>
        <div class="cart-total">
            <h3>Total: <span id="cart-total-price">₹0.00</span></h3>
            <a href="checkout.html" class="btn" id="checkout-btn">Checkout</a>
        </div>
    </div>
    
    <!-- Contact page content -->
    <section class="contact" style="padding-top: 12rem;">
        <h1 class="heading">Contact <span>Us</span></h1>
        
        <!-- Contact form -->
        <div class="contact-form">
            <h2 style="text-align: center; margin-bottom: 2rem; font-size: 2.5rem; color: var(--black);">Get In Touch</h2>
            <form id="contact-form">
                <div class="form-group">
                    <label for="name">Full Name *</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="subject">Subject *</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                <div class="form-group">
                    <label for="message">Message *</label>
                    <textarea id="message" name="message" placeholder="Tell us how we can help you..." required></textarea>
                </div>
                <button type="submit" class="btn" style="width: 100%; margin-top: 1rem;">Send Message</button>
            </form>
        </div>
        
        <!-- Contact information -->
        <div class="contact-info">
            <div class="contact-box">
                <i class="fa fa-map-marker-alt"></i>
                <h3>Our Location</h3>
                <p>123 Fresh Street<br>Bhubaneswar, Odisha 751001<br>India</p>
            </div>
            <div class="contact-box">
                <i class="fa fa-phone"></i>
                <h3>Phone Number</h3>
                <p>+****************<br>+****************</p>
            </div>
            <div class="contact-box">
                <i class="fa fa-envelope"></i>
                <h3>Email Address</h3>
                <p><EMAIL><br><EMAIL></p>
            </div>
            <div class="contact-box">
                <i class="fa fa-clock"></i>
                <h3>Business Hours</h3>
                <p>Mon - Fri: 8:00 AM - 8:00 PM<br>Sat - Sun: 9:00 AM - 6:00 PM</p>
            </div>
        </div>
    </section>
    
    <!-- Footer section -->
    <section class="footer">
        <div class="box-container">
            <div class="box">
                <h3>Quick Links</h3>
                <a href="index.html"><i class="fa fa-arrow-right"></i> Home</a>
                <a href="products.html"><i class="fa fa-arrow-right"></i> Products</a>
                <a href="about.html"><i class="fa fa-arrow-right"></i> About Us</a>
                <a href="contact.html"><i class="fa fa-arrow-right"></i> Contact Us</a>
            </div>
            <div class="box">
                <h3>Contact Info</h3>
                <a href="#"><i class="fa fa-phone"></i> +************</a>
                <a href="#"><i class="fa fa-envelope"></i> <EMAIL></a>
                <a href="#"><i class="fa fa-map-marker-alt"></i> Bhubaneswar, India - 751001</a>
            </div>
            <div class="box">
                <h3>Follow Us</h3>
                <a href="#"><i class="fab fa-facebook-f"></i> Facebook</a>
                <a href="#"><i class="fab fa-twitter"></i> Twitter</a>
                <a href="#"><i class="fab fa-instagram"></i> Instagram</a>
                <a href="#"><i class="fab fa-linkedin"></i> LinkedIn</a>
            </div>
        </div>
        <div class="credit">Created by <span>Fresh Picks</span> | All Rights Reserved</div>
    </section>

    <!-- JavaScript file -->
    <script src="js/script.js"></script>
    <script>
        // Contact form handling
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const name = formData.get('name');
            const email = formData.get('email');
            const subject = formData.get('subject');
            const message = formData.get('message');
            
            // Simple validation
            if (!name || !email || !subject || !message) {
                showNotification('Please fill in all required fields.');
                return;
            }
            
            // Simulate form submission
            showNotification('Thank you for your message! We\'ll get back to you soon.');
            
            // Reset form
            this.reset();
        });
    </script>
</body>
</html>
