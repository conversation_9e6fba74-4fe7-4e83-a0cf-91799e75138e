# PowerShell script to create placeholder images for the grocery store
# This creates simple colored rectangles as placeholder images

# Create image directory if it doesn't exist
if (!(Test-Path "image")) {
    New-Item -ItemType Directory -Path "image"
}

# Function to create a simple colored image using .NET
function Create-PlaceholderImage {
    param(
        [string]$filename,
        [int]$width,
        [int]$height,
        [string]$color,
        [string]$text
    )
    
    Add-Type -AssemblyName System.Drawing
    
    # Create bitmap
    $bitmap = New-Object System.Drawing.Bitmap($width, $height)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set background color
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromName($color))
    $graphics.FillRectangle($brush, 0, 0, $width, $height)
    
    # Add text
    $font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $textSize = $graphics.MeasureString($text, $font)
    $x = ($width - $textSize.Width) / 2
    $y = ($height - $textSize.Height) / 2
    $graphics.DrawString($text, $font, $textBrush, $x, $y)
    
    # Save image
    $bitmap.Save("image\$filename", [System.Drawing.Imaging.ImageFormat]::Jpeg)
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $textBrush.Dispose()
    $font.Dispose()
    
    Write-Host "Created: image\$filename"
}

# Create placeholder images
Write-Host "Creating placeholder images..."

Create-PlaceholderImage -filename "banner.jpg" -width 800 -height 400 -color "Green" -text "Fresh Groceries"
Create-PlaceholderImage -filename "category-1.jpg" -width 300 -height 200 -color "Red" -text "Fresh Fruits"
Create-PlaceholderImage -filename "category-2.jpg" -width 300 -height 200 -color "Green" -text "Vegetables"
Create-PlaceholderImage -filename "category-3.jpg" -width 300 -height 200 -color "Blue" -text "Dairy"
Create-PlaceholderImage -filename "category-4.jpg" -width 300 -height 200 -color "Orange" -text "Bakery"
Create-PlaceholderImage -filename "apple.jpg" -width 200 -height 150 -color "Red" -text "Apples"
Create-PlaceholderImage -filename "banana.jpg" -width 200 -height 150 -color "Yellow" -text "Bananas"
Create-PlaceholderImage -filename "carrot.jpg" -width 200 -height 150 -color "Orange" -text "Carrots"
Create-PlaceholderImage -filename "broccoli.jpg" -width 200 -height 150 -color "Green" -text "Broccoli"
Create-PlaceholderImage -filename "milk.jpg" -width 200 -height 150 -color "LightGray" -text "Milk"
Create-PlaceholderImage -filename "cheese.jpg" -width 200 -height 150 -color "Gold" -text "Cheese"
Create-PlaceholderImage -filename "bread.jpg" -width 200 -height 150 -color "Brown" -text "Bread"
Create-PlaceholderImage -filename "croissant.jpg" -width 200 -height 150 -color "Orange" -text "Croissant"

# Create new Indian product images
Write-Host "Creating Indian product images..."

# Vegetables
Create-PlaceholderImage -filename "tomatoes.jpg" -width 200 -height 150 -color "Red" -text "Tomatoes"
Create-PlaceholderImage -filename "onions.jpg" -width 200 -height 150 -color "Purple" -text "Onions"
Create-PlaceholderImage -filename "potatoes.jpg" -width 200 -height 150 -color "Brown" -text "Potatoes"
Create-PlaceholderImage -filename "spinach.jpg" -width 200 -height 150 -color "DarkGreen" -text "Spinach"

# Dairy Products
Create-PlaceholderImage -filename "paneer.jpg" -width 200 -height 150 -color "LightGray" -text "Paneer"
Create-PlaceholderImage -filename "yogurt.jpg" -width 200 -height 150 -color "LightBlue" -text "Yogurt"
Create-PlaceholderImage -filename "butter.jpg" -width 200 -height 150 -color "Yellow" -text "Butter"
Create-PlaceholderImage -filename "ghee.jpg" -width 200 -height 150 -color "Orange" -text "Ghee"

# Fruits
Create-PlaceholderImage -filename "mangoes.jpg" -width 200 -height 150 -color "Orange" -text "Mangoes"
Create-PlaceholderImage -filename "oranges.jpg" -width 200 -height 150 -color "Orange" -text "Oranges"
Create-PlaceholderImage -filename "grapes.jpg" -width 200 -height 150 -color "Purple" -text "Grapes"
Create-PlaceholderImage -filename "pomegranates.jpg" -width 200 -height 150 -color "DarkRed" -text "Pomegranates"

# Bakery Items
Create-PlaceholderImage -filename "naan.jpg" -width 200 -height 150 -color "Brown" -text "Naan"
Create-PlaceholderImage -filename "cookies.jpg" -width 200 -height 150 -color "SandyBrown" -text "Cookies"
Create-PlaceholderImage -filename "biscuits.jpg" -width 200 -height 150 -color "Brown" -text "Biscuits"
Create-PlaceholderImage -filename "cake.jpg" -width 200 -height 150 -color "Brown" -text "Cake"

Write-Host "All placeholder images created successfully!"
Write-Host "Total images created: 29 (13 existing + 16 new Indian products)"
Write-Host "You can now refresh your browser to see the images."
