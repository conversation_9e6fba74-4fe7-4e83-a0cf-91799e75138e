// Fresh Picks Grocery Store JavaScript

// Global variables
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let products = [];

// Sample product data
const sampleProducts = [
    {
        id: 1,
        name: "Fresh Apples",
        price: 3.99,
        image: "image/apple1.jpg",
        category: "fruits",
        discount: "10% off",
        rating: 4.5,
        
    },
    {
        id: 2,
        name: "Organic Bananas",
        price: 2.49,
        image: "image/banana.jpg",
        category: "fruits",
        discount: "",
        rating: 4.2,
        
    },
    {
        id: 3,
        name: "Fresh Carrots",
        price: 1.99,
        image: "image/carrot.jpg",
        category: "vegetables",
        discount: "15% off",
        rating: 4.3,
       
    },
    {
        id: 4,
        name: "<PERSON><PERSON><PERSON><PERSON>",
        price: 2.99,
        image: "image/broccoli.jpg",
        category: "vegetables",
        discount: "",
        rating: 4.1,
        
    },
    {
        id: 5,
        name: "Whole Milk",
        price: 3.49,
        image: "image/milk.jpg",
        category: "dairy",
        discount: "",
        rating: 4.4,
        
    },
    {
        id: 6,
        name: " Cheese",
        price: 4.99,
        image: "image/cheese.jpg",
        category: "dairy",
        discount: "20% off",
        rating: 4.6,
        
    },
    {
        id: 7,
        name: "Fresh Bread",
        price: 2.99,
        image: "image/bread.jpg",
        category: "bakery",
        discount: "",
        rating: 4.3,
        
    },
    {
        id: 8,
        name: "Croissants",
        price: 5.99,
        image: "image/croissant.jpg",
        category: "bakery",
        discount: "10% off",
        rating: 4.7,
       
    },
    // Indian Vegetables
    {
        id: 9,
        name: "Fresh Tomatoes",
        price: 40.00,
        image: "image/tomatoes.jpg",
        category: "vegetables",
        discount: "15% off",
        rating: 4.3,
        
    },
    {
        id: 10,
        name: "Onions",
        price: 30.00,
        image: "image/onions.jpg",
        category: "vegetables",
        discount: "",
        rating: 4.2,
        
    },
    {
        id: 11,
        name: "Potatoes",
        price: 25.00,
        image: "image/potatoes.jpg",
        category: "vegetables",
        discount: "10% off",
        rating: 4.1,
        
    },
    {
        id: 12,
        name: "Fresh Spinach",
        price: 35.00,
        image: "image/spinach.jpg",
        category: "vegetables",
        discount: "",
        rating: 4.4,
        
    },
    // Indian Dairy Products
    {
        id: 13,
        name: "Fresh Paneer",
        price: 180.00,
        image: "image/paneer.jpg",
        category: "dairy",
        discount: "20% off",
        rating: 4.6,
        
    },
    {
        id: 14,
        name: "Yogurt (Dahi)",
        price: 60.00,
        image: "image/yogurt.jpg",
        category: "dairy",
        discount: "",
        rating: 4.5,
        
    },
    {
        id: 15,
        name: "Pure Butter",
        price: 120.00,
        image: "image/butter.jpg",
        category: "dairy",
        discount: "15% off",
        rating: 4.3,
        
    },
    {
        id: 16,
        name: "Pure Ghee",
        price: 450.00,
        image: "image/ghee.jpg",
        category: "dairy",
        discount: "",
        rating: 4.8,
        
    },
    // Indian Fruits
    {
        id: 17,
        name: "Fresh Mangoes",
        price: 80.00,
        image: "image/mangoes.jpg",
        category: "fruits",
        discount: "10% off",
        rating: 4.7,
        
    },
    {
        id: 18,
        name: "Fresh Oranges",
        price: 50.00,
        image: "image/oranges.jpg",
        category: "fruits",
        discount: "",
        rating: 4.2,
        
    },
    {
        id: 19,
        name: "Green Grapes",
        price: 90.00,
        image: "image/grapes.jpg",
        category: "fruits",
        discount: "20% off",
        rating: 4.4,
        
    },
    {
        id: 20,
        name: "Pomegranates",
        price: 150.00,
        image: "image/pomegranates.jpg",
        category: "fruits",
        discount: "",
        rating: 4.6,
        
    },
    // Indian Bakery Items
    {
        id: 21,
        name: "Fresh Bread",
        price: 55.00,
        image: "image/naan.jpg",
        category: "bakery",
        discount: "15% off",
        rating: 4.3,
        
    },
    {
        id: 22,
        name: " Cookies",
        price: 80.00,
        image: "image/cookies.jpg",
        category: "bakery",
        discount: "",
        rating: 4.1,
        
    },
    {
        id: 23,
        name: "Digestive Biscuits",
        price: 45.00,
        image: "image/biscuits.jpg",
        category: "bakery",
        discount: "10% off",
        rating: 4.0,
       
    },
    {
        id: 24,
        name: "Chocolate Cake",
        price: 250.00,
        image: "image/cake.jpg",
        category: "bakery",
        discount: "",
        rating: 4.5,
        
    }
];

// DOM elements
const cartBtn = document.getElementById('cart-btn');
const cartSidebar = document.querySelector('.cart-sidebar');
const closeCartBtn = document.getElementById('close-cart');
const cartItems = document.querySelector('.cart-items');
const cartCount = document.querySelector('.cart-count');
const cartTotalPrice = document.getElementById('cart-total-price');
const searchBox = document.getElementById('search-box');
const searchResults = document.getElementById('search-results');
const productSlider = document.querySelector('.product-slider');
const menuBtn = document.getElementById('menu-btn');
const navbar = document.querySelector('.navbar');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    products = sampleProducts;
    loadFeaturedProducts();
    updateCartUI();
    setupEventListeners();

    // Debug: Log image paths to console
    console.log('Fresh Picks Grocery Store - Debug Info:');
    console.log('Products loaded:', products.length);
    console.log('Sample image paths:');
    products.slice(0, 3).forEach(product => {
        console.log(`- ${product.name}: ${product.image}`);
    });
});

// Event listeners setup
function setupEventListeners() {
    // Cart functionality
    cartBtn.addEventListener('click', toggleCart);
    closeCartBtn.addEventListener('click', toggleCart);

    // Mobile menu
    menuBtn.addEventListener('click', toggleMobileMenu);

    // Search functionality
    searchBox.addEventListener('input', handleSearch);
    searchBox.addEventListener('focus', showSearchResults);
    document.addEventListener('click', hideSearchResults);

    // Category buttons
    document.querySelectorAll('[data-category]').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            filterProductsByCategory(e.target.dataset.category);
        });
    });

    // Prevent search form submission
    document.querySelector('.search-form form').addEventListener('submit', (e) => {
        e.preventDefault();
    });
}

// Cart functionality
function toggleCart() {
    cartSidebar.classList.toggle('active');
}

function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1
        });
    }

    saveCart();
    updateCartUI();
    showNotification(`${product.name} added to cart!`);
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCart();
    updateCartUI();
}

function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;

    item.quantity += change;

    if (item.quantity <= 0) {
        removeFromCart(productId);
    } else {
        saveCart();
        updateCartUI();
    }
}

function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

// Currency formatting function
function formatPrice(price) {
    const numPrice = parseFloat(price);
    if (numPrice >= 1000) {
        return `₹${numPrice.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `₹${numPrice.toFixed(2)}`;
}

// Extract quantity/weight information from product description
function extractQuantity(description) {
    // Look for patterns like "1kg", "500ml", "250g pack", etc.
    const quantityPatterns = [
        /(\d+(?:\.\d+)?\s*(?:kg|g|ml|l|pack|piece|pieces|bundle))/i,
        /(\d+(?:\.\d+)?\s*(?:gram|grams|kilogram|kilograms|liter|liters|milliliter|milliliters))/i
    ];

    for (let pattern of quantityPatterns) {
        const match = description.match(pattern);
        if (match) {
            return match[1];
        }
    }

    // Fallback: extract common quantity indicators
    if (description.includes('1kg')) return '1kg';
    if (description.includes('500g')) return '500g';
    if (description.includes('250g')) return '250g';
    if (description.includes('500ml')) return '500ml';
    if (description.includes('200g')) return '200g';
    if (description.includes('pack')) return 'pack';
    if (description.includes('bundle')) return 'bundle';

    return 'per unit'; // Default fallback
}

function updateCartUI() {
    // Update cart count
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;

    // Update cart total
    const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartTotalPrice.textContent = formatPrice(totalPrice);

    // Update cart items display
    cartItems.innerHTML = '';

    if (cart.length === 0) {
        cartItems.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">Your cart is empty</p>';
        return;
    }

    cart.forEach(item => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <img src="${item.image}" alt="${item.name}"
                 onerror="this.src='data:image/svg+xml;base64,${btoa(`<svg width="70" height="70" xmlns="http://www.w3.org/2000/svg"><rect width="70" height="70" fill="#f0f0f0" stroke="#ddd"/><text x="35" y="35" text-anchor="middle" dy=".3em" font-family="Arial" font-size="10" fill="#666">${item.name}</text></svg>`)}'">
            <div class="cart-item-content">
                <h3>${item.name}</h3>
                <div class="price">${formatPrice(item.price)}</div>
                <div class="quantity">
                    <button onclick="updateQuantity(${item.id}, -1)">-</button>
                    <span>${item.quantity}</span>
                    <button onclick="updateQuantity(${item.id}, 1)">+</button>
                </div>
            </div>
            <div class="remove-item" onclick="removeFromCart(${item.id})">
                <i class="fa fa-trash"></i>
            </div>
        `;
        cartItems.appendChild(cartItem);
    });
}

// Mobile menu functionality
function toggleMobileMenu() {
    navbar.classList.toggle('active');
}

// Search functionality
function handleSearch() {
    const query = searchBox.value.toLowerCase().trim();

    if (query.length === 0) {
        searchResults.innerHTML = '';
        searchResults.style.display = 'none';
        return;
    }

    const filteredProducts = products.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query)
    );

    displaySearchResults(filteredProducts);
}

function displaySearchResults(results) {
    searchResults.innerHTML = '';

    if (results.length === 0) {
        searchResults.innerHTML = '<div style="padding: 1rem; text-align: center; color: #666;">No products found</div>';
    } else {
        results.slice(0, 5).forEach(product => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-item';
            resultItem.innerHTML = `
                <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/50x50?text=No+Image'">
                <div class="content">
                    <h4>${product.name}</h4>
                    <p class="search-quantity">${extractQuantity(product.description)}</p>
                    <div class="search-price-section">
                        <div class="search-price-discount-row">
                            <div class="price">$${product.price.toFixed(2)}</div>
                            ${product.discount ? `<div class="search-discount">${product.discount}</div>` : ''}
                        </div>
                        <div class="search-rating">
                            ${generateStars(product.rating)} <span>(${product.rating})</span>
                        </div>
                    </div>
                </div>
            `;
            resultItem.addEventListener('click', () => {
                addToCart(product.id);
                searchBox.value = '';
                searchResults.style.display = 'none';
            });
            searchResults.appendChild(resultItem);
        });
    }

    searchResults.style.display = 'block';
}

function showSearchResults() {
    if (searchBox.value.trim().length > 0) {
        searchResults.style.display = 'block';
    }
}

function hideSearchResults(e) {
    if (!e.target.closest('.search-form')) {
        searchResults.style.display = 'none';
    }
}

// Product display functionality
function loadFeaturedProducts() {
    displayProducts(products);
    loadHeroProducts();
}

// Load product images for hero section (images only, no details)
function loadHeroProducts() {
    const heroImagesContainer = document.getElementById('hero-product-images');

    if (!heroImagesContainer) return;

    // Get a mix of products for display (6-8 products)
    const allProducts = [...products];

    // Prioritize Indian products and products with good images
    const indianProducts = allProducts.filter(p => p.id >= 9);
    const internationalProducts = allProducts.filter(p => p.id < 9);

    // Select 6 products: 4 Indian + 2 international
    const selectedProducts = [
        ...indianProducts.slice(0, 4),
        ...internationalProducts.slice(0, 2)
    ];

    // Clear container and add images
    heroImagesContainer.innerHTML = '';

    selectedProducts.forEach((product, index) => {
        const img = document.createElement('img');
        img.className = 'hero-product-image';
        img.src = product.image;
        img.alt = product.name;
        img.title = `${product.name} - ${formatPrice(product.price)}`;

        // Remove click functionality - images are display-only
        // No click event listeners added

        // Add error handling
        img.onerror = function() {
            this.src = `data:image/svg+xml;base64,${btoa(`
                <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100" height="100" fill="#f0f0f0" stroke="#ddd"/>
                    <text x="50" y="50" text-anchor="middle" dy=".3em" font-family="Arial" font-size="10" fill="#666">
                        ${product.name}
                    </text>
                </svg>
            `)}`;
        };

        // Add loading animation
        img.style.opacity = '0';
        img.style.transform = 'scale(0.8)';

        setTimeout(() => {
            img.style.transition = 'all 0.4s ease';
            img.style.opacity = '1';
            img.style.transform = 'scale(1)';
        }, index * 100);

        heroImagesContainer.appendChild(img);
    });
}

function createHeroProductCard(product) {
    const card = document.createElement('div');
    card.className = 'hero-product-card';

    // Add animation delay for staggered loading effect
    const randomDelay = Math.random() * 0.3;
    card.style.animationDelay = `${randomDelay}s`;

    card.innerHTML = `
        <img src="${product.image}" alt="${product.name}"
             onerror="handleImageError(this, '${product.name}')"
             onload="handleImageLoad(this)">
        <h4>${product.name}</h4>
        <div class="price">${formatPrice(product.price)}</div>
        ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
        <div style="font-size: 1.2rem; color: #666; margin-bottom: 0.5rem; line-height: 1.3;">${product.description}</div>
        <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            <div class="stars" style="margin-right: 0.5rem;">
                ${generateStars(product.rating)}
            </div>
            <span style="font-size: 1.1rem; color: #666;">(${product.rating})</span>
        </div>
        <button class="btn" onclick="addToCart(${product.id})"
                onmouseover="this.textContent='Add to Cart 🛒'"
                onmouseout="this.textContent='Add to Cart'">Add to Cart</button>
    `;

    // Add entrance animation
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';

    setTimeout(() => {
        card.style.transition = 'all 0.6s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, randomDelay * 1000);

    return card;
}

function displayProducts(productsToShow) {
    productSlider.innerHTML = '';

    if (productsToShow.length === 0) {
        productSlider.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem; grid-column: 1/-1;">No products found</p>';
        return;
    }

    productsToShow.forEach(product => {
        const productBox = document.createElement('div');
        productBox.className = 'box';
        productBox.innerHTML = `
            ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
            <div class="image-container" style="position: relative;">
                <img src="${product.image}" alt="${product.name}"
                     onerror="handleImageError(this, '${product.name}')"
                     onload="handleImageLoad(this)">
                <div class="image-loading" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #666;">Loading...</div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-quantity">${extractQuantity(product.description)}</p>
                <div class="product-price-section">
                    <div class="price-discount-row">
                        <div class="price">${formatPrice(product.price)}</div>
                        ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                    </div>
                    <div class="rating-inline">
                        ${generateStars(product.rating)} <span>(${product.rating})</span>
                    </div>
                </div>
            </div>
            <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
        `;
        productSlider.appendChild(productBox);
    });
}

// Image handling functions
function handleImageError(img, productName) {
    console.log(`🔴 IMAGE ERROR: Failed to load image: ${img.src} for product: ${productName}`);
    console.log(`🔍 Image element:`, img);
    console.log(`🔍 Current working directory: ${window.location.origin}`);

    // Create a more visible fallback
    img.src = `data:image/svg+xml;base64,${btoa(`
        <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="150" fill="#f0f0f0" stroke="#ddd" stroke-width="2"/>
            <text x="100" y="60" text-anchor="middle" dy=".3em" font-family="Arial" font-size="12" fill="#666">
                ${productName}
            </text>
            <text x="100" y="90" text-anchor="middle" dy=".3em" font-family="Arial" font-size="10" fill="#999">
                Image not found
            </text>
        </svg>
    `)}`;
    img.style.backgroundColor = '#f8f8f8';
    img.style.border = '2px dashed #ccc';
}

function handleImageLoad(img) {
    console.log(`✅ IMAGE SUCCESS: Loaded image: ${img.src}`);
    const loadingDiv = img.parentNode.querySelector('.image-loading');
    if (loadingDiv) {
        loadingDiv.style.display = 'none';
    }
}

function filterProductsByCategory(category) {
    const filteredProducts = products.filter(product => product.category === category);
    displayProducts(filteredProducts);

    // Scroll to products section
    document.getElementById('featured').scrollIntoView({ behavior: 'smooth' });

    // Update heading
    const heading = document.querySelector('#featured .heading');
    heading.innerHTML = `${category.charAt(0).toUpperCase() + category.slice(1)} <span>Products</span>`;
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';

    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fa fa-star"></i>';
    }

    if (hasHalfStar) {
        starsHTML += '<i class="fa fa-star-half-alt"></i>';
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }

    return starsHTML;
}

// Utility functions
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--green);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        z-index: 10000;
        font-size: 1.4rem;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
        animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.navbar a').forEach(link => {
    link.addEventListener('click', () => {
        navbar.classList.remove('active');
    });
});

// Close cart when clicking outside
document.addEventListener('click', (e) => {
    if (!e.target.closest('.cart-sidebar') && !e.target.closest('#cart-btn')) {
        cartSidebar.classList.remove('active');
    }
});

// Debug function to test image loading
function testImageLoading() {
    console.log('Testing image loading...');
    const testImage = new Image();
    testImage.onload = function() {
        console.log('✓ Test image loaded successfully:', this.src);
    };
    testImage.onerror = function() {
        console.log('✗ Test image failed to load:', this.src);
    };
    testImage.src = 'image/banner.jpg';

    // Test a few product images
    ['apple.jpg', 'banana.jpg', 'carrot.jpg'].forEach(img => {
        const testImg = new Image();
        testImg.onload = () => console.log(`✓ ${img} loaded successfully`);
        testImg.onerror = () => console.log(`✗ ${img} failed to load`);
        testImg.src = `image/${img}`;
    });
}

// Call test function after a short delay
setTimeout(testImageLoading, 1000);