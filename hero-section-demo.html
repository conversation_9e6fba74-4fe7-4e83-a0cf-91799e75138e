<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section Layout Improvements Demo - Fresh Picks</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        .demo-container {
            padding: 2rem;
            max-width: 120rem;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 4rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .demo-title {
            color: var(--green);
            font-size: 3rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .demo-subtitle {
            color: var(--black);
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--green);
            padding-bottom: 0.5rem;
        }
        
        .demo-info {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--green);
        }
        
        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .improvement-item {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
            text-align: center;
        }
        
        .improvement-item h3 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .improvement-item p {
            color: #666;
            font-size: 1.4rem;
            line-height: 1.6;
        }
        
        .improvement-item .icon {
            font-size: 3rem;
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .before, .after {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
        }
        
        .before h3 {
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .after h3 {
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .layout-demo {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            border: 2px solid var(--green);
        }
        
        .layout-demo h4 {
            color: var(--green);
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .layout-visual {
            display: grid;
            grid-template-columns: 60% 40%;
            gap: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--green) 0%, var(--dark-green) 100%);
            border-radius: 1rem;
            margin: 1rem 0;
            min-height: 200px;
            align-items: center;
        }
        
        .layout-visual .text-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            max-width: 85%;
        }
        
        .layout-visual .image-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 0.5rem;
            height: 120px;
        }
        
        .layout-visual .image-placeholder {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
        }
        
        .spacing-indicator {
            position: relative;
            margin: 1rem 0;
        }
        
        .spacing-indicator::before {
            content: attr(data-spacing);
            position: absolute;
            top: -1.5rem;
            left: 0;
            font-size: 1rem;
            color: var(--green);
            font-weight: 600;
            background: #fff;
            padding: 0.2rem 0.5rem;
            border-radius: 0.2rem;
            border: 1px solid var(--green);
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
            
            .improvement-list {
                grid-template-columns: 1fr;
            }
            
            .layout-visual {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .layout-visual .text-section {
                max-width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header section with logo and navigation -->
    <header class="header">
        <div class="header-left">
            <a href="index.html" class="logo">
                <i class="fa fa-shopping-basket"></i> Fresh Picks
            </a>
            
            <!-- Navigation menu -->
            <nav class="navbar">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact Us</a></li>
                </ul>
            </nav>
        </div>
        
        <div class="header-center">
            <!-- Search form -->
            <div class="search-form">
                <form>
                    <input type="search" id="search-box" placeholder="Search products...">
                    <button type="submit"><i class="fa fa-search"></i></button>
                </form>
                <div class="search-results" id="search-results"></div>
            </div>
            
            <!-- Shopping cart icon with item count -->
            <div class="icons">
                <div id="cart-btn" class="fa fa-shopping-cart"><span class="cart-count">0</span></div>
            </div>
        </div>
        
        <div class="header-right">
            <!-- Mobile menu toggle -->
            <div class="icons">
                <div id="menu-btn" class="fa fa-bars"></div>
            </div>
        </div>
    </header>

    <!-- Hero Section - Live Example -->
    <section class="hero-new" id="hero">
        <div class="hero-container">
            <div class="hero-left">
                <h2>Fresh & Organic Groceries Delivered</h2>
                <p>Get the freshest produce, organic foods, and daily essentials delivered right to your doorstep. Quality guaranteed, convenience delivered.</p>
                <a href="products.html" class="btn btn-large">Shop Now</a>
            </div>
            <div class="hero-right" id="hero-products">
                <!-- Product images will be loaded here -->
            </div>
        </div>
    </section>

    <div class="demo-container">
        <h1 class="demo-title">🎨 Hero Section Layout Improvements</h1>
        
        <!-- Overview Section -->
        <div class="demo-section">
            <h2 class="demo-subtitle">📋 Overview</h2>
            <div class="demo-info">
                <p><strong>Improvements Made:</strong> Adjusted text alignment and vertical spacing in the Fresh Picks hero section to create a more balanced and professional layout.</p>
                <p><strong>Goals Achieved:</strong></p>
                <ul style="text-align: left; color: #666; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li>Moved text content further left for better visual balance</li>
                    <li>Ensured consistent left-alignment of all text elements</li>
                    <li>Balanced top and bottom spacing for better vertical centering</li>
                    <li>Maintained the 60/40 split layout and responsive functionality</li>
                </ul>
            </div>
        </div>
        
        <!-- Key Improvements -->
        <div class="demo-section">
            <h2 class="demo-subtitle">✨ Key Improvements</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">⬅️</div>
                    <h3>Enhanced Left Positioning</h3>
                    <p>Reduced max-width from 90% to 85% and removed auto margins to position text content further left for better visual balance</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📐</div>
                    <h3>Balanced Vertical Spacing</h3>
                    <p>Adjusted hero section padding from 12rem/4rem to 10rem/6rem for more balanced top and bottom spacing</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📝</div>
                    <h3>Consistent Text Alignment</h3>
                    <p>Ensured all text elements (h2, p, button) are properly left-aligned with full width for consistent positioning</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Responsive Optimization</h3>
                    <p>Updated mobile styles to maintain improved spacing while preserving center alignment for mobile devices</p>
                </div>
            </div>
        </div>
        
        <!-- Before vs After -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🔄 Before vs After Comparison</h2>
            <div class="before-after">
                <div class="before">
                    <h3>❌ Before (Unbalanced Layout)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Text content centered with auto margins</li>
                        <li>Unbalanced vertical spacing (12rem top, 4rem bottom)</li>
                        <li>Text container at 90% width</li>
                        <li>Inconsistent text element positioning</li>
                        <li>Less optimal use of left side space</li>
                    </ul>
                </div>
                <div class="after">
                    <h3>✅ After (Balanced Layout)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Text content positioned further left</li>
                        <li>Balanced vertical spacing (10rem top, 6rem bottom)</li>
                        <li>Text container at 85% width with left positioning</li>
                        <li>Consistent left-alignment for all text elements</li>
                        <li>Better visual balance and professional appearance</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Layout Visualization -->
        <div class="demo-section">
            <h2 class="demo-subtitle">📐 Layout Visualization</h2>
            
            <div class="layout-demo">
                <h4>Improved 60/40 Split Layout</h4>
                <div class="layout-visual">
                    <div class="text-section">
                        <h3 style="margin-bottom: 1rem; font-size: 1.8rem;">Text Content (60%)</h3>
                        <p style="margin-bottom: 1rem; font-size: 1.2rem; opacity: 0.9;">• Positioned further left (85% width)</p>
                        <p style="margin-bottom: 1rem; font-size: 1.2rem; opacity: 0.9;">• Consistent left-alignment</p>
                        <p style="margin-bottom: 1rem; font-size: 1.2rem; opacity: 0.9;">• Balanced vertical spacing</p>
                        <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 25px; display: inline-block; font-size: 1rem;">Button</div>
                    </div>
                    <div class="image-section">
                        <div class="image-placeholder">Img 1</div>
                        <div class="image-placeholder">Img 2</div>
                        <div class="image-placeholder">Img 3</div>
                        <div class="image-placeholder">Img 4</div>
                        <div class="image-placeholder">Img 5</div>
                        <div class="image-placeholder">Img 6</div>
                    </div>
                </div>
                <div class="spacing-indicator" data-spacing="Balanced 10rem top / 6rem bottom padding"></div>
            </div>
        </div>
        
        <!-- Technical Implementation -->
        <div class="demo-section">
            <h2 class="demo-subtitle">⚙️ Technical Implementation</h2>
            <div class="demo-info">
                <h3 style="color: var(--green); margin-bottom: 1rem;">CSS Changes Made:</h3>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">1. Hero Section Spacing:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
/* Before */
.hero-new {
    padding: 12rem 9% 4rem; /* Unbalanced spacing */
    min-height: 250px;
    max-height: 300px;
}

/* After */
.hero-new {
    padding: 10rem 5% 6rem; /* Balanced spacing */
    min-height: 280px;
    max-height: 350px;
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">2. Text Content Positioning:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
/* Before */
.hero-left {
    max-width: 90%;
    margin: auto 0; /* Centered positioning */
}

/* After */
.hero-left {
    max-width: 85%; /* Reduced width */
    margin: 0; /* Removed auto margins */
    margin-right: auto; /* Left positioning */
    padding: 3rem 2rem; /* Increased vertical padding */
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">3. Text Element Alignment:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
.hero-left h2,
.hero-left p {
    text-align: left;
    width: 100%; /* Ensure full width for proper alignment */
}

.btn-large {
    align-self: flex-start; /* Maintain left alignment */
    display: inline-block; /* Prevent full width stretching */
}
                    </pre>
                </div>
            </div>
        </div>
        
        <!-- Benefits -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🎯 Benefits Achieved</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">⚖️</div>
                    <h3>Better Visual Balance</h3>
                    <p>Text content positioned further left creates better balance with the product images on the right side</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📏</div>
                    <h3>Improved Spacing</h3>
                    <p>Balanced top and bottom padding creates more professional vertical spacing and better content centering</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📝</div>
                    <h3>Consistent Alignment</h3>
                    <p>All text elements properly left-aligned within their container for clean, professional appearance</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Responsive Excellence</h3>
                    <p>Improvements maintained across all device sizes while preserving mobile-optimized center alignment</p>
                </div>
            </div>
        </div>
        
        <!-- Live Demo Instructions -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🧪 Live Demo</h2>
            <div class="demo-info">
                <h4 style="color: var(--green); margin-bottom: 1rem;">Test the Improved Hero Section:</h4>
                <ul style="text-align: left; color: #666; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li><strong>Desktop:</strong> Notice the improved left positioning and balanced vertical spacing</li>
                    <li><strong>Text Alignment:</strong> Observe consistent left-alignment of heading, paragraph, and button</li>
                    <li><strong>Visual Balance:</strong> See how text content balances better with product images</li>
                    <li><strong>Responsive:</strong> Resize browser to test mobile responsiveness</li>
                    <li><strong>Spacing:</strong> Notice the more professional vertical spacing</li>
                </ul>
                
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 1.5rem; border-radius: 0.5rem; margin: 2rem 0; text-align: center;">
                    <h4 style="color: #856404; margin-bottom: 1rem;">Compare Pages:</h4>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="index.html" class="btn" style="padding: 0.8rem 1.5rem; font-size: 1.2rem;">Homepage Hero</a>
                        <a href="products.html" class="btn" style="padding: 0.8rem 1.5rem; font-size: 1.2rem;">Products Page</a>
                    </div>
                    <p style="color: #856404; margin-top: 1rem; font-size: 1.2rem;">Scroll up to see the live hero section with improvements applied!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button class="close-cart" id="close-cart">&times;</button>
        </div>
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be added here -->
        </div>
        <div class="cart-total">
            <div class="total-price" id="total-price">Total: ₹0.00</div>
            <button class="btn checkout-btn">Checkout</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
</body>
</html>
