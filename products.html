<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Fresh Picks Online Grocery Store</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
</head>
<body>
    <!-- Header section with logo and navigation -->
    <header class="header">
        <div class="header-left">
            <a href="index.html" class="logo">
                <i class="fa fa-shopping-basket"></i> Fresh Picks
            </a>
        </div>

        <div class="header-center">
            <!-- Search form -->
            <div class="search-form">
                <form>
                    <input type="search" id="search-box" placeholder="Search products...">
                    <button type="submit"><i class="fa fa-search"></i></button>
                </form>
                <div class="search-results" id="search-results"></div>
            </div>

            <!-- Shopping cart icon with item count -->
            <div class="icons">
                <div id="cart-btn" class="fa fa-shopping-cart"><span class="cart-count">0</span></div>
            </div>
        </div>

        <div class="header-right">
            <!-- Mobile menu toggle -->
            <div class="icons">
                <div id="menu-btn" class="fa fa-bars"></div>
            </div>

            <!-- Navigation menu -->
            <nav class="navbar">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Products</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact Us</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <!-- Shopping cart sidebar -->
    <div class="cart-sidebar">
        <div class="cart-header">
            <h3>Your Cart</h3>
            <div id="close-cart" class="fa fa-times"></div>
        </div>
        <div class="cart-items">
            <!-- Cart items will be dynamically added here -->
        </div>
        <div class="cart-total">
            <h3>Total: <span id="cart-total-price">₹0.00</span></h3>
            <a href="checkout.html" class="btn" id="checkout-btn">Checkout</a>
        </div>
    </div>
    
    <!-- Products page content -->
    <section class="products" style="padding-top: 12rem;">
        <h1 class="heading">Our <span>Products</span></h1>
        
        <!-- Category filter buttons -->
        <div class="category-filters" style="text-align: center; margin-bottom: 3rem;">
            <button class="btn filter-btn active" data-filter="all">All Products</button>
            <button class="btn filter-btn" data-filter="fruits">Fruits</button>
            <button class="btn filter-btn" data-filter="vegetables">Vegetables</button>
            <button class="btn filter-btn" data-filter="dairy">Dairy</button>
            <button class="btn filter-btn" data-filter="bakery">Bakery</button>
        </div>
        
        <!-- Products grid -->
        <div class="product-grid" id="product-grid">
            <!-- Products will be dynamically loaded here -->
        </div>
    </section>
    
    <!-- Footer section -->
    <section class="footer">
        <div class="box-container">
            <div class="box">
                <h3>Quick Links</h3>
                <a href="index.html"><i class="fa fa-arrow-right"></i> Home</a>
                <a href="products.html"><i class="fa fa-arrow-right"></i> Products</a>
                <a href="about.html"><i class="fa fa-arrow-right"></i> About Us</a>
                <a href="contact.html"><i class="fa fa-arrow-right"></i> Contact Us</a>
            </div>
            <div class="box">
                <h3>Contact Info</h3>
                <a href="#"><i class="fa fa-phone"></i> +************</a>
                <a href="#"><i class="fa fa-envelope"></i> <EMAIL></a>
                <a href="#"><i class="fa fa-map-marker-alt"></i> Bhubaneswar, India - 751001</a>
            </div>
            <div class="box">
                <h3>Follow Us</h3>
                <a href="#"><i class="fab fa-facebook-f"></i> Facebook</a>
                <a href="#"><i class="fab fa-twitter"></i> Twitter</a>
                <a href="#"><i class="fab fa-instagram"></i> Instagram</a>
                <a href="#"><i class="fab fa-linkedin"></i> LinkedIn</a>
            </div>
        </div>
        <div class="credit">Created by <span>Fresh Picks</span> | All Rights Reserved</div>
    </section>

    <!-- JavaScript file -->
    <script src="js/script.js"></script>
    <script>
        // Products page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadAllProducts();
            setupProductFilters();

            // Check for URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category');
            const highlightId = urlParams.get('highlight');

            if (category) {
                // Filter products by category and activate the corresponding button
                const filteredProducts = products.filter(product => product.category === category);
                displayProductsInGrid(filteredProducts);

                // Activate the corresponding filter button
                const filterButtons = document.querySelectorAll('.filter-btn');
                filterButtons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.filter === category) {
                        btn.classList.add('active');
                    }
                });
            }

            // Check for highlight parameter (from search results)
            if (highlightId) {
                const productId = parseInt(highlightId);
                // Highlight the specific product after a short delay to ensure DOM is ready
                setTimeout(() => {
                    highlightProduct(productId);
                    // Clean up URL parameter
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);
                }, 500);
            }
        });
        
        function loadAllProducts() {
            const productGrid = document.getElementById('product-grid');
            displayProductsInGrid(products);
        }
        
        function displayProductsInGrid(productsToShow) {
            const productGrid = document.getElementById('product-grid');
            productGrid.innerHTML = '';
            
            if (productsToShow.length === 0) {
                productGrid.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No products found</p>';
                return;
            }
            
            productsToShow.forEach(product => {
                const productBox = document.createElement('div');
                productBox.className = 'box';
                productBox.innerHTML = `
                    ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
                    <div class="image-container" style="position: relative;">
                        <img src="${product.image}" alt="${product.name}" 
                             onerror="handleImageError(this, '${product.name}')"
                             onload="handleImageLoad(this)">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <p class="product-quantity">${extractQuantity(product.description)}</p>
                        <div class="product-price-section">
                            <div class="price-discount-row">
                                <div class="price">${formatPrice(product.price)}</div>
                                ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                            </div>
                            <div class="rating-inline">
                                ${generateStars(product.rating)} <span>(${product.rating})</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
                `;
                productGrid.appendChild(productBox);
            });
        }
        
        function setupProductFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    const filter = this.dataset.filter;
                    
                    if (filter === 'all') {
                        displayProductsInGrid(products);
                    } else {
                        const filteredProducts = products.filter(product => product.category === filter);
                        displayProductsInGrid(filteredProducts);
                    }
                });
            });
        }
    </script>
</body>
</html>
