/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Base styles and reset */
* {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
  border: none;
  text-decoration: none;
  text-transform: capitalize;
  transition: all .2s linear;
}

:root {
  --green: #27ae60;
  --dark-green: #219150;
  --black: #444;
  --light-color: #666;
  --border: .1rem solid rgba(0,0,0,.1);
  --border-hover: .1rem solid var(--black);
  --box-shadow: 0 .5rem 1rem rgba(0,0,0,.1);
}

html {
  font-size: 62.5%;
  overflow-x: hidden;
  scroll-behavior: smooth;
  scroll-padding-top: 7rem;
}

body {
  background: #f7f7f7;
}

section {
  padding: 2rem 9%;
}

.btn {
  display: inline-block;
  margin-top: 1rem;
  padding: .8rem 3rem;
  background: var(--green);
  color: #fff;
  font-size: 1.7rem;
  cursor: pointer;
  border-radius: .5rem;
}

.btn:hover {
  background: var(--dark-green);
}

.heading {
  text-align: center;
  padding: 2rem 0;
  padding-bottom: 3rem;
  font-size: 3.5rem;
  color: var(--black);
}

.heading span {
  background: var(--green);
  color: #fff;
  display: inline-block;
  padding: .5rem 3rem;
  clip-path: polygon(100% 0, 93% 50%, 100% 99%, 0% 100%, 7% 50%, 0% 0%);
}

/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 5%; /* Reduced from 9% to 5% for better space utilization */
  background: #fff;
  box-shadow: var(--box-shadow);
}

/* Header layout sections */
.header-left {
  display: flex;
  align-items: center;
  gap: 3rem; /* Add spacing between logo and navigation menu */
}

.header-center {
  display: flex;
  align-items: center;
  gap: 1rem; /* Reduced gap between search bar and cart icon */
  flex: 1;
  justify-content: center;
  max-width: 60rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem; /* Add spacing between navigation menu and mobile menu button */
}

.header .logo {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--black);
}

.header .logo i {
  color: var(--green);
  margin-right: .5rem;
}

.header .navbar {
  display: flex;
  align-items: center;
}

.header .navbar ul {
  display: flex;
  list-style: none;
  align-items: baseline; /* Ensure perfect baseline alignment */
  margin: 0;
  padding: 0;
  height: 100%;
}

.header .navbar ul li {
  margin-left: 2rem; /* Reduced from 2.5rem to 2rem for more compact spacing */
  display: flex;
  align-items: center; /* Center each menu item vertically */
  height: 100%;
}

.header .navbar ul li:first-child {
  margin-left: 0;
}

.header .navbar ul li a {
  font-size: 1.7rem;
  color: var(--black);
  text-decoration: none;
  padding: 1rem 0; /* Consistent vertical padding for alignment */
  display: flex;
  align-items: center; /* Perfect vertical centering */
  height: 100%;
  line-height: 1; /* Consistent line height for baseline alignment */
  transition: color 0.3s ease;
}

.header .navbar ul li a:hover,
.header .navbar ul li a.active {
  color: var(--green);
}

.header .icons {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced gap for more compact layout */
}

.header .icons div {
  height: 4.5rem;
  width: 4.5rem;
  line-height: 4.5rem;
  border-radius: .5rem;
  background: #eee;
  color: var(--black);
  font-size: 2rem;
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.header .icons div:hover {
  background: var(--green);
  color: #fff;
}

.header .icons .cart-count {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: var(--green);
  color: #fff;
  font-size: 1.2rem;
  height: 2rem;
  width: 2rem;
  line-height: 2rem;
  border-radius: 50%;
}

#menu-btn {
  display: none;
}

.header .search-form {
  width: 40rem; /* Optimized width for compact layout */
  height: 5rem;
  border-radius: .5rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  background: #eee;
  flex-shrink: 1; /* Allow search form to shrink if needed */
}

.header .search-form form {
  width: 100%;
  display: flex;
}

.header .search-form input {
  font-size: 1.6rem;
  padding: 0 1.2rem;
  height: 100%;
  width: 100%;
  background: none;
  color: var(--black);
}

.header .search-form button {
  font-size: 2.2rem;
  padding: 0 1.5rem;
  color: var(--black);
  background: #eee;
  cursor: pointer;
}

.header .search-form button:hover {
  color: var(--green);
}

/* Cart sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -100%;
  width: 35rem;
  height: 100vh;
  background: #fff;
  z-index: 1001;
  padding: 2rem;
  box-shadow: -2px 0 5px rgba(0,0,0,0.1);
  transition: right 0.3s ease;
}

.cart-sidebar.active {
  right: 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: var(--border);
}

.cart-header h3 {
  font-size: 2rem;
  color: var(--black);
}

#close-cart {
  font-size: 2rem;
  cursor: pointer;
  color: var(--black);
}

#close-cart:hover {
  color: var(--green);
}

.cart-items {
  max-height: calc(100vh - 15rem);
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 0;
  border-bottom: var(--border);
}

.cart-item img {
  height: 7rem;
  width: 7rem;
  object-fit: cover;
}

.cart-item-content {
  flex: 1;
}

.cart-item-content h3 {
  font-size: 1.7rem;
  color: var(--black);
}

.cart-item-content .price {
  font-size: 1.5rem;
  color: var(--green);
}

.cart-item-content .quantity {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.cart-item-content .quantity span {
  font-size: 1.4rem;
  color: var(--light-color);
}

.cart-item-content .quantity button {
  background: #eee;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.4rem;
}

.cart-item-content .quantity button:hover {
  background: var(--green);
  color: #fff;
}

.cart-item .remove-item {
  font-size: 1.7rem;
  cursor: pointer;
  color: #ff6b6b;
}

.cart-total {
  margin-top: 2rem;
  text-align: center;
}

.cart-total h3 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: 1rem;
}

/* Redesigned Hero Banner Section - 2 Column Layout */
.hero-new {
  padding: 10rem 5% 6rem; /* Balanced top and bottom padding, reduced horizontal padding */
  background: linear-gradient(135deg, var(--green) 0%, var(--dark-green) 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
  min-height: 280px; /* Increased min-height for better content spacing */
  max-height: 350px; /* Increased max-height to accommodate better spacing */
  margin-top: 0; /* Ensure no additional margin */
}

.hero-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-container {
  display: grid;
  grid-template-columns: 60% 40%;
  gap: 3rem;
  max-width: 140rem;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  height: 100%; /* Use full height of parent */
  align-items: center; /* Center content vertically */
  min-height: 220px; /* Increased minimum height for better spacing */
}

/* Hero Left Column - Text Content - Removed duplicate definition */

/* Hero Right Column - Product Images Grid */
.hero-right {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 180px; /* Increased height to prevent image cropping */
  align-self: center;
  max-width: 95%;
}

/* Product Image Styling */
.hero-product-image {
  width: 100%;
  height: 100%;
  min-height: 70px; /* Increased minimum height */
  object-fit: contain; /* Changed from cover to contain to show full images */
  object-position: center; /* Center the image within container */
  border-radius: 0.6rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: block;
  background: rgba(255, 255, 255, 0.1); /* Light background for better image visibility */
  cursor: default; /* Remove pointer cursor since images are non-interactive */
}

/* Hero Left Column - Text Content */
.hero-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 3rem 2rem; /* Increased vertical padding for better balance */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 85%; /* Reduced from 90% to move content further left */
  margin: 0; /* Removed auto margins to position content more to the left */
  margin-right: auto; /* Keep right margin auto to maintain left positioning */
  height: fit-content;
  min-height: 200px; /* Ensure minimum height for proper vertical centering */
}

.hero-left h2 {
  font-size: 3.5rem;
  margin-bottom: 1.2rem; /* Slightly increased for better spacing */
  color: #fff;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -1px;
  text-align: left;
  width: 100%; /* Ensure full width for proper left alignment */
}

.hero-left p {
  font-size: 1.6rem;
  margin-bottom: 2rem; /* Increased margin for better button spacing */
  color: #fff;
  opacity: 0.95;
  font-weight: 300;
  line-height: 1.4;
  text-align: left;
  width: 100%; /* Ensure full width for proper left alignment */
}

.btn-large {
  padding: 1.2rem 3.5rem;
  font-size: 1.6rem;
  background: #fff;
  color: var(--green);
  font-weight: 700;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  align-self: flex-start; /* Maintain left alignment within container */
  margin-top: 0.5rem; /* Small top margin for spacing from paragraph */
  display: inline-block; /* Ensure button doesn't stretch full width */
}

.btn-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.btn-large:hover::before {
  left: 100%;
}

.btn-large:hover {
  background: #f8f8f8;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Enhanced Hero Product Cards */
.hero-product-card {
  background: rgba(255, 255, 255, 0.98);
  color: var(--black);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 1rem;
  text-align: left;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.hero-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--green), var(--dark-green));
}

.hero-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  background: rgba(255, 255, 255, 1);
}

.hero-product-card img {
  width: 100%;
  height: 10rem;
  object-fit: cover;
  border-radius: 0.8rem;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.hero-product-card:hover img {
  transform: scale(1.05);
}

.hero-product-card h4 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--black);
  font-weight: 600;
  line-height: 1.3;
}

.hero-product-card .price {
  font-size: 1.6rem;
  color: var(--green);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.hero-product-card .discount {
  display: inline-block;
  background: #e74c3c;
  color: #fff;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-product-card .btn {
  padding: 0.8rem 1.5rem;
  font-size: 1.3rem;
  margin-top: 1rem;
  width: 100%;
  border-radius: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.hero-product-card .btn:hover {
  background: var(--dark-green);
  transform: translateY(-2px);
}

/* Hero section animations */
@keyframes heroFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heroSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes heroSlideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-center {
  animation: heroFadeIn 0.8s ease 0.2s both;
}

.hero-left {
  animation: heroSlideIn 0.8s ease 0.4s both;
}

.hero-right {
  animation: heroSlideInRight 0.8s ease 0.6s both;
}

/* Loading state for hero products */
.hero-products-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.4rem;
}

.hero-products-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  margin-left: 10px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced star ratings in hero cards */
.hero-product-card .stars i {
  color: #ffd700;
  font-size: 1.2rem;
  margin-right: 2px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Pulse effect for discount badges */
.hero-product-card .discount {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Categories section */
.categories .box-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.categories .box-container .box {
  padding: 3rem 2rem;
  border-radius: .5rem;
  background: #fff;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.categories .box-container .box img {
  height: 15rem;
  width: 100%;
  object-fit: contain; /* Changed from cover to contain to prevent cropping */
  object-position: center; /* Center the image within container */
  border-radius: .5rem;
  background: #f8f9fa; /* Light background for better image visibility */
  border: 1px solid #e9ecef; /* Subtle border for definition */
}

.categories .box-container .box h3 {
  font-size: 2rem;
  color: var(--black);
  padding: 1rem 0;
}

/* Category box hover effects */
.categories .box-container .box {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.categories .box-container .box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.categories .box-container .box:hover img {
  transform: scale(1.05);
}

.categories .box-container .box img {
  transition: transform 0.3s ease;
}

/* Products section styles removed - featured products section no longer exists */

/* Image container for better layout control */
.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  min-height: 12rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  object-position: center;
}



/* Footer section */
.footer {
  background: #fff;
  padding-bottom: 9rem;
}

.footer .box-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.footer .box-container .box h3 {
  font-size: 2.2rem;
  color: var(--black);
  padding: 1rem 0;
}

.footer .box-container .box a {
  display: block;
  font-size: 1.4rem;
  color: var(--light-color);
  padding: 1rem 0;
}

.footer .box-container .box a i {
  color: var(--green);
  padding-right: .5rem;
}

.footer .box-container .box a:hover i {
  padding-right: 2rem;
}

.footer .credit {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  padding-top: 2.5rem;
  font-size: 2rem;
  color: var(--black);
  border-top: var(--border);
}

.footer .credit span {
  color: var(--green);
}

/* Media queries for responsiveness */
@media (max-width: 991px) {
  html {
    font-size: 55%;
  }

  .header {
    padding: 1.2rem 3%; /* Reduced horizontal padding for tablets */
  }
  
  section {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  #menu-btn {
    display: inline-block;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    padding: 1.2rem 3%; /* Consistent with tablet padding */
  }

  .header-left,
  .header-center,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .header-center {
    order: 2;
    flex-direction: column;
    gap: 1rem;
  }

  .header-left {
    order: 1;
  }

  .header-right {
    order: 3;
    justify-content: space-between;
  }

  .header .search-form {
    width: 100%;
  }

  .header .navbar {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border-top: var(--border);
    border-bottom: var(--border);
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: .2s linear;
  }

  .header .navbar.active {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .header .navbar ul {
    flex-direction: column;
    padding: 1rem 0;
    align-items: center;
  }

  .header .navbar ul li {
    margin: 0;
    text-align: center;
    width: 100%;
  }

  .header .navbar ul li a {
    font-size: 2rem;
    display: block;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    width: 100%;
    text-align: center;
  }

  .header .navbar ul li:last-child a {
    border-bottom: none;
  }

  .hero-new {
    padding: 8rem 2rem 5rem; /* Balanced top and bottom padding for mobile */
    height: auto;
    max-height: none;
    min-height: auto;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 100%;
    min-height: auto;
  }

  .hero-left {
    order: 1;
    text-align: center;
    padding: 2rem 1.5rem; /* Increased vertical padding for mobile */
    align-items: center; /* Center content on mobile */
    max-width: 100%; /* Full width on mobile */
    margin: 0; /* Remove auto margins on mobile */
  }

  .hero-right {
    order: 2;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    height: 200px; /* Increased height for mobile to prevent cropping */
    padding: 1rem;
    align-self: center;
    max-width: 100%;
  }

  .hero-left h2 {
    font-size: 2.8rem;
    line-height: 1.1;
    text-align: center;
  }

  .hero-left p {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .btn-large {
    padding: 1.2rem 3rem;
    font-size: 1.5rem;
    align-self: center; /* Center button on mobile */
  }

  .hero-product-card {
    padding: 1.2rem;
    margin-bottom: 1.2rem;
  }

  .hero-product-card img {
    height: 8rem;
  }

  .hero-product-card h4 {
    font-size: 1.4rem;
  }

  .hero-product-card .price {
    font-size: 1.5rem;
  }

  /* Mobile responsive for simplified product cards */
  .product-name {
    font-size: 1.6rem;
  }

  .product-quantity {
    font-size: 1.2rem;
  }

  .product-price-section .price {
    font-size: 1.6rem;
  }

  .product-price-section {
    min-height: 5rem; /* Reduced height for mobile */
  }

  .product-grid .box {
    min-height: 30rem; /* Reduced card height for mobile */
  }

  .price-discount-row {
    gap: 0.5rem; /* Smaller gap on mobile */
  }

  .add-to-cart-btn {
    font-size: 1.3rem;
    padding: 0.8rem;
  }

  /* Mobile responsive product images */
  .product-grid .box .image-container {
    height: 10rem;
    margin-bottom: 1rem;
  }

  .product-grid .box img {
    max-height: 100%;
    max-width: 100%;
  }

  .product-grid .box {
    padding: 1.5rem 1rem;
    min-height: 28rem;
  }

  .cart-sidebar {
    width: 30rem;
  }
}

@media (max-width: 450px) {
  html {
    font-size: 50%;
  }

  .header {
    padding: 1.2rem 3%; /* Consistent padding across all mobile breakpoints */
    gap: 1rem;
  }

  .header .search-form {
    width: 100%;
    margin: 0;
  }

  .header .navbar {
    position: absolute;
    top: 99%;
    left: 0;
    right: 0;
    background: #fff;
    border-top: .1rem solid rgba(0,0,0,.1);
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    box-shadow: var(--box-shadow);
  }

  .header .navbar.active {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .header .navbar ul {
    flex-direction: column;
    padding: 1rem 0;
  }

  .header .navbar ul li {
    margin: 0;
    text-align: center;
  }

  .header .navbar ul li a {
    font-size: 2rem;
    display: block;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .header .navbar ul li:last-child a {
    border-bottom: none;
  }

  .hero {
    padding: 15rem 2rem;
  }

  .hero .content h3 {
    font-size: 2.5rem;
  }

  .categories .box-container {
    grid-template-columns: 1fr;
  }



  .cart-sidebar {
    width: 100%;
  }
}

/* Additional utility classes */
.hidden {
  display: none !important;
}

.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* Product card hover effects removed - featured products section no longer exists */

/* Highlighted product styles for search navigation */
.highlighted-product {
  animation: highlightPulse 2s ease-in-out;
  border: 3px solid var(--green) !important;
  box-shadow: 0 0 20px rgba(39, 174, 96, 0.3) !important;
  transform: scale(1.02) !important;
  z-index: 10 !important;
  position: relative !important;
}

@keyframes highlightPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
    border-color: var(--green);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0.2);
    border-color: var(--green);
  }
  100% {
    box-shadow: 0 0 20px rgba(39, 174, 96, 0.3);
    border-color: var(--green);
  }
}

/* Ensure highlighted product is visible above others */
.product-grid .highlighted-product {
  z-index: 100;
}

/* Touch device considerations */
@media (hover: none) and (pointer: coarse) {
  /* On touch devices, reduce hover effects to prevent sticky hover states */
  .product-grid .box:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .search-results .search-item:hover {
    transform: none;
  }
}

/* Desktop hover enhancements */
@media (hover: hover) and (pointer: fine) {
  .product-grid .box:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

/* Cart item animations */
.cart-item {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Search results styling */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: var(--border);
  border-top: none;
  max-height: 30rem;
  overflow-y: auto;
  z-index: 1000;
}

.search-results .search-item {
  padding: 1rem;
  border-bottom: var(--border);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-results .search-item {
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.search-results .search-item:hover {
  background: #f8f8f8;
  transform: scale(1.01);
}

/* Search result image container */
.search-image-container {
  width: 5rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
  overflow: hidden;
  flex-shrink: 0;
}

.search-results .search-item img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  object-position: center;
  border-radius: 0.3rem;
}

.search-results .search-item .content h4 {
  font-size: 1.6rem;
  color: var(--black);
}

.search-results .search-item .content .price {
  font-size: 1.4rem;
  color: var(--green);
  font-weight: 600;
}

/* Simplified Product Card Styling */
.product-info {
  text-align: left;
  padding: 1rem 0;
}

.product-name {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.3;
}

.product-quantity {
  font-size: 1.3rem;
  color: var(--light-color);
  margin-bottom: 0.8rem;
  font-weight: 500;
}

.product-price-section {
  margin-bottom: 1.5rem;
  min-height: 6rem; /* Fixed height to ensure consistent button alignment */
}

.price-discount-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.product-price-section .price {
  font-size: 1.8rem;
  color: var(--green);
  font-weight: 700;
  margin: 0;
}

.discount-badge {
  display: inline-block;
  background: #e74c3c;
  color: #fff;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.rating-inline {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.5rem;
}

.rating-inline span {
  font-size: 1.1rem;
  color: var(--light-color);
  font-weight: 500;
}

.rating-inline .stars i {
  font-size: 1.2rem;
  color: #ffd700;
}

.add-to-cart-btn {
  width: 100%;
  text-align: center;
  margin-top: auto; /* Push button to bottom of card */
  padding: 1rem;
  font-size: 1.4rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Simplified Search Results Styling */
.search-quantity {
  font-size: 1.2rem;
  color: #888;
  margin: 0.3rem 0;
  font-weight: 500;
}

.search-price-section {
  margin-top: 0.5rem;
}

.search-price-discount-row {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.3rem;
  flex-wrap: wrap;
}

.search-price-section .price {
  font-size: 1.4rem;
  color: var(--green);
  font-weight: 600;
  margin: 0;
}

.search-discount {
  display: inline-block;
  background: #e74c3c;
  color: #fff;
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.search-rating {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.3rem;
}

.search-rating span {
  font-size: 1rem;
  color: #666;
}

.search-rating .stars i {
  font-size: 1.1rem;
  color: #ffd700;
}

/* Search action hint styling removed - hint text no longer displayed */

/* Products page specific styles */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.product-grid .box {
  position: relative;
  background: #fff;
  border-radius: .5rem;
  padding: 2rem 1.5rem;
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 35rem; /* Consistent card height for button alignment */
}

/* Product Grid Image Container */
.product-grid .box .image-container {
  width: 100%;
  height: 12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

/* Product Grid Image Styling */
.product-grid .box img {
  max-height: 100%;
  max-width: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  object-position: center;
  display: block;
  border-radius: 0.3rem;
}

/* Product card hover effects - entire card scales on hover */
.product-grid .box {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-grid .box:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 5;
}

/* Remove individual image hover effect since entire card now hovers */
.product-grid .box img {
  transition: none; /* Remove image-specific transition */
}

.category-filters {
  margin-bottom: 3rem;
}

.filter-btn {
  margin: 0.5rem;
  background: #fff;
  color: var(--black);
  border: var(--border);
}

.filter-btn.active,
.filter-btn:hover {
  background: var(--green);
  color: #fff;
}

/* Active navigation link */
.navbar ul li a.active {
  color: var(--green);
}

/* About page styles */
.about-content {
  max-width: 80rem;
  margin: 0 auto;
  text-align: center;
}

.about-content h2 {
  font-size: 3rem;
  color: var(--black);
  margin-bottom: 2rem;
}

.about-content p {
  font-size: 1.6rem;
  color: var(--light-color);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.feature-box {
  background: #fff;
  padding: 3rem 2rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.feature-box i {
  font-size: 4rem;
  color: var(--green);
  margin-bottom: 1rem;
}

.feature-box h3 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: 1rem;
}

.feature-box p {
  font-size: 1.4rem;
  color: var(--light-color);
  line-height: 1.6;
}

/* Contact page styles */
.contact-form {
  max-width: 60rem;
  margin: 0 auto;
  background: #fff;
  padding: 3rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
}

.contact-form .form-group {
  margin-bottom: 2rem;
}

.contact-form label {
  display: block;
  font-size: 1.6rem;
  color: var(--black);
  margin-bottom: 0.5rem;
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 1rem;
  font-size: 1.6rem;
  border: var(--border);
  border-radius: 0.5rem;
  background: #f8f8f8;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: var(--green);
  background: #fff;
}

.contact-form textarea {
  height: 12rem;
  resize: vertical;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.contact-box {
  background: #fff;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.contact-box i {
  font-size: 3rem;
  color: var(--green);
  margin-bottom: 1rem;
}

.contact-box h3 {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: 1rem;
}

.contact-box p {
  font-size: 1.4rem;
  color: var(--light-color);
}
