<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Image Sizing Demo - Fresh Picks</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        .demo-header {
            background: var(--green);
            color: #fff;
            padding: 3rem 2rem;
            text-align: center;
            margin-top: 8rem;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .demo-header p {
            font-size: 1.6rem;
            opacity: 0.9;
            max-width: 80rem;
            margin: 0 auto;
        }
        
        .comparison-section {
            padding: 3rem 2rem;
            background: #f8f9fa;
        }
        
        .comparison-section h2 {
            text-align: center;
            color: var(--black);
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        
        .improvements-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .improvement-item {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
            text-align: center;
        }
        
        .improvement-item h3 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .improvement-item p {
            color: #666;
            font-size: 1.4rem;
            line-height: 1.6;
        }
        
        .improvement-item .icon {
            font-size: 3rem;
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .size-demo {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
            text-align: center;
        }
        
        .size-demo h3 {
            color: var(--black);
            margin-bottom: 1rem;
        }
        
        .size-demo .demo-image {
            width: 100%;
            height: 12rem;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: #666;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <!-- Demo Header -->
    <div class="demo-header">
        <h1>📏 Product Image Sizing Improvements</h1>
        <p>
            Optimized product image dimensions for better visual hierarchy and balanced grid layout 
            across all product displays in the Fresh Picks grocery website.
        </p>
    </div>
    
    <!-- Improvements Overview -->
    <div class="comparison-section">
        <h2>Key Improvements Made</h2>
        <div class="improvements-list">
            <div class="improvement-item">
                <div class="icon">📐</div>
                <h3>Proper Sizing</h3>
                <p>Reduced image height from 15rem to 12rem (desktop) and 10rem (mobile) for better proportions</p>
            </div>
            <div class="improvement-item">
                <div class="icon">📱</div>
                <h3>Responsive Design</h3>
                <p>Optimized image dimensions for both desktop and mobile devices with appropriate scaling</p>
            </div>
            <div class="improvement-item">
                <div class="icon">🎯</div>
                <h3>Consistent Layout</h3>
                <p>Uniform image dimensions across all product displays for professional grid appearance</p>
            </div>
            <div class="improvement-item">
                <div class="icon">⚖️</div>
                <h3>Visual Balance</h3>
                <p>Images now complement rather than dominate the product cards, improving overall hierarchy</p>
            </div>
        </div>
        
        <!-- Size Demonstration -->
        <h2>Image Size Specifications</h2>
        <div class="demo-grid">
            <div class="size-demo">
                <h3>Desktop Images</h3>
                <div class="demo-image">
                    12rem height × 18rem max-width
                </div>
                <p>Optimal size for desktop product grid with proper aspect ratio maintenance</p>
            </div>
            <div class="size-demo">
                <h3>Mobile Images</h3>
                <div class="demo-image" style="height: 10rem;">
                    10rem height × 15rem max-width
                </div>
                <p>Reduced dimensions for mobile devices ensuring good visibility and touch interaction</p>
            </div>
        </div>
    </div>

    <!-- Live Product Grid Demo -->
    <section class="products" style="background: #fff; padding: 3rem 2rem;">
        <h1 class="heading">Improved <span>Product Grid</span></h1>
        <p style="text-align: center; margin-bottom: 3rem; font-size: 1.4rem; color: #666;">
            See the optimized product image sizing in action with our sample products
        </p>
        <div class="product-grid" id="demo-product-grid">
            <!-- Products will be loaded here -->
        </div>
    </section>
    
    <!-- Technical Details -->
    <div class="comparison-section">
        <h2>Technical Implementation</h2>
        <div style="background: #fff; padding: 2rem; border-radius: 1rem; box-shadow: var(--box-shadow); margin: 2rem 0;">
            <h3 style="color: var(--green); margin-bottom: 1rem;">CSS Changes Applied:</h3>
            <pre style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; overflow-x: auto; font-size: 1.2rem; line-height: 1.6;">
/* Desktop Product Images */
.products .product-slider .box img,
.product-grid .box img {
    height: 12rem;
    width: 100%;
    max-width: 18rem;
    object-fit: contain;
    object-position: center;
    margin: 0 auto 1rem;
    border-radius: 0.5rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .products .product-slider .box img,
    .product-grid .box img {
        height: 10rem;
        max-width: 15rem;
    }
}
            </pre>
        </div>
        
        <div style="background: #fff; padding: 2rem; border-radius: 1rem; box-shadow: var(--box-shadow);">
            <h3 style="color: var(--green); margin-bottom: 1rem;">Key Features:</h3>
            <ul style="font-size: 1.4rem; line-height: 1.8; color: #666;">
                <li>✅ <strong>Proportional Scaling:</strong> Images maintain aspect ratio without distortion</li>
                <li>✅ <strong>Consistent Dimensions:</strong> Uniform sizing across all product displays</li>
                <li>✅ <strong>Responsive Behavior:</strong> Optimized for desktop, tablet, and mobile devices</li>
                <li>✅ <strong>Visual Hierarchy:</strong> Images complement rather than dominate product cards</li>
                <li>✅ <strong>Professional Layout:</strong> Balanced grid appearance with proper spacing</li>
                <li>✅ <strong>Hover Effects:</strong> Smooth scaling animations on image hover</li>
            </ul>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Demo-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadDemoProductGrid();
        });
        
        function loadDemoProductGrid() {
            const demoContainer = document.getElementById('demo-product-grid');
            if (!demoContainer) return;
            
            // Show a selection of products to demonstrate the improved sizing
            const demoProducts = products.slice(0, 8);
            
            demoContainer.innerHTML = '';
            demoProducts.forEach(product => {
                const productBox = document.createElement('div');
                productBox.className = 'box';
                productBox.innerHTML = `
                    ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
                    <div class="image-container">
                        <img src="${product.image}" alt="${product.name}" 
                             onerror="handleImageError(this, '${product.name}')"
                             onload="handleImageLoad(this)">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <p class="product-quantity">${extractQuantity(product.description)}</p>
                        <div class="product-price-section">
                            <div class="price-discount-row">
                                <div class="price">${formatPrice(product.price)}</div>
                                ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                            </div>
                            <div class="rating-inline">
                                ${generateStars(product.rating)} <span>(${product.rating})</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
                `;
                demoContainer.appendChild(productBox);
            });
        }
    </script>
</body>
</html>
