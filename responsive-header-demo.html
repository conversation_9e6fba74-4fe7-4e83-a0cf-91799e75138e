<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Header Layout Demo - Fresh Picks</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        .demo-container {
            padding: 10rem 2rem 2rem;
            max-width: 120rem;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 4rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .demo-title {
            color: var(--green);
            font-size: 3rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .demo-subtitle {
            color: var(--black);
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--green);
            padding-bottom: 0.5rem;
        }
        
        .demo-info {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--green);
        }
        
        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .improvement-item {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
            text-align: center;
        }
        
        .improvement-item h3 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .improvement-item p {
            color: #666;
            font-size: 1.4rem;
            line-height: 1.6;
        }
        
        .improvement-item .icon {
            font-size: 3rem;
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .before, .after {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
        }
        
        .before h3 {
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .after h3 {
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .responsive-demo {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            border: 2px solid var(--green);
        }
        
        .responsive-demo h4 {
            color: var(--green);
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .breakpoint-visual {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .breakpoint-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .breakpoint-card h5 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 1.6rem;
        }
        
        .breakpoint-card .layout-preview {
            background: #fff;
            padding: 1rem;
            border-radius: 0.3rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            min-height: 6rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 1.2rem;
        }
        
        .layout-preview .search-bar {
            background: var(--green);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 0.2rem;
            flex: 1;
            margin-right: 0.5rem;
        }
        
        .layout-preview .icons-group {
            display: flex;
            gap: 0.5rem;
        }
        
        .layout-preview .icon {
            background: #666;
            color: white;
            padding: 0.3rem 0.5rem;
            border-radius: 0.2rem;
            font-size: 1rem;
        }
        
        .responsive-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 2rem 0;
        }
        
        .responsive-instructions h4 {
            color: #856404;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
            
            .improvement-list {
                grid-template-columns: 1fr;
            }
            
            .breakpoint-visual {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header section with logo and navigation -->
    <header class="header">
        <div class="header-left">
            <a href="index.html" class="logo">
                <i class="fa fa-shopping-basket"></i> Fresh Picks
            </a>
            
            <!-- Navigation menu -->
            <nav class="navbar">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact Us</a></li>
                </ul>
            </nav>
        </div>
        
        <div class="header-center">
            <!-- Search form -->
            <div class="search-form">
                <form>
                    <input type="search" id="search-box" placeholder="Search products...">
                    <button type="submit"><i class="fa fa-search"></i></button>
                </form>
                <div class="search-results" id="search-results"></div>
            </div>
            
            <!-- Shopping cart icon and mobile menu (for mobile/tablet layout) -->
            <div class="icons">
                <div id="cart-btn" class="fa fa-shopping-cart"><span class="cart-count">0</span></div>
                <div id="menu-btn" class="fa fa-bars"></div>
            </div>
        </div>
        
        <div class="header-right">
            <!-- Navigation menu -->
            <nav class="navbar">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact Us</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="demo-container">
        <h1 class="demo-title">📱 Responsive Header Layout Improvements</h1>
        
        <!-- Overview Section -->
        <div class="demo-section">
            <h2 class="demo-subtitle">📋 Overview</h2>
            <div class="demo-info">
                <p><strong>Improvements Made:</strong> Modified the Fresh Picks grocery website header layout for mobile and tablet responsive breakpoints to improve space utilization and visual organization.</p>
                <p><strong>Key Changes:</strong></p>
                <ul style="text-align: left; color: #666; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li>Reduced search bar width on mobile and tablet devices</li>
                    <li>Aligned cart icon and hamburger menu on the same horizontal line</li>
                    <li>Optimized layout for three responsive breakpoints</li>
                    <li>Maintained all existing functionality while improving space efficiency</li>
                </ul>
            </div>
        </div>
        
        <!-- Key Improvements -->
        <div class="demo-section">
            <h2 class="demo-subtitle">✨ Key Improvements</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">📏</div>
                    <h3>Reduced Search Bar Width</h3>
                    <p>Decreased search form width from 100% to 70% (tablet) and 65% (mobile) to create space for cart and menu icons</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">🔗</div>
                    <h3>Aligned Cart & Menu Icons</h3>
                    <p>Positioned shopping cart icon and hamburger menu button on the same horizontal line for better space efficiency</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Three Breakpoint Optimization</h3>
                    <p>Customized layouts for tablet (991px), mobile (768px), and small mobile (450px) breakpoints</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">⚙️</div>
                    <h3>Maintained Functionality</h3>
                    <p>Preserved all search, cart, and mobile menu interactions while improving visual organization</p>
                </div>
            </div>
        </div>
        
        <!-- Before vs After -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🔄 Before vs After Comparison</h2>
            <div class="before-after">
                <div class="before">
                    <h3>❌ Before (Inefficient Layout)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Search bar took full width (100%)</li>
                        <li>Cart and menu icons on separate lines/sections</li>
                        <li>Wasted horizontal space on mobile/tablet</li>
                        <li>Less efficient use of available screen space</li>
                        <li>Vertical layout created unnecessary height</li>
                    </ul>
                </div>
                <div class="after">
                    <h3>✅ After (Optimized Layout)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Search bar width reduced (70% tablet, 65% mobile)</li>
                        <li>Cart and menu icons aligned on same horizontal line</li>
                        <li>Better utilization of horizontal space</li>
                        <li>More compact and efficient layout</li>
                        <li>Improved visual organization and balance</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Responsive Breakpoints -->
        <div class="demo-section">
            <h2 class="demo-subtitle">📐 Responsive Breakpoints</h2>
            
            <div class="responsive-demo">
                <h4>Layout Optimization Across Breakpoints</h4>
                <div class="breakpoint-visual">
                    <div class="breakpoint-card">
                        <h5>Tablet (≤ 991px)</h5>
                        <div class="layout-preview">
                            <div class="search-bar">Search (70%)</div>
                            <div class="icons-group">
                                <div class="icon">🛒</div>
                            </div>
                        </div>
                        <p style="color: #666; font-size: 1.2rem;">Search and cart on same line, menu hidden</p>
                    </div>
                    
                    <div class="breakpoint-card">
                        <h5>Mobile (≤ 768px)</h5>
                        <div class="layout-preview">
                            <div class="search-bar">Search (65%)</div>
                            <div class="icons-group">
                                <div class="icon">🛒</div>
                                <div class="icon">☰</div>
                            </div>
                        </div>
                        <p style="color: #666; font-size: 1.2rem;">Search, cart, and menu all on same line</p>
                    </div>
                    
                    <div class="breakpoint-card">
                        <h5>Small Mobile (≤ 450px)</h5>
                        <div class="layout-preview">
                            <div class="search-bar">Search (60%)</div>
                            <div class="icons-group">
                                <div class="icon">🛒</div>
                                <div class="icon">☰</div>
                            </div>
                        </div>
                        <p style="color: #666; font-size: 1.2rem;">Further reduced search width for small screens</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Implementation -->
        <div class="demo-section">
            <h2 class="demo-subtitle">⚙️ Technical Implementation</h2>
            <div class="demo-info">
                <h3 style="color: var(--green); margin-bottom: 1rem;">CSS Changes Made:</h3>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">1. Tablet Layout (991px):</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
@media (max-width: 991px) {
  .header-center {
    flex-direction: row; /* Keep search and cart on same line */
    gap: 1rem;
    align-items: center;
  }
  
  .header .search-form {
    width: 70%; /* Reduced from 100% */
    flex-shrink: 1;
  }
  
  .header-center .icons {
    flex-shrink: 0; /* Prevent cart icon from shrinking */
  }
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">2. Mobile Layout (768px):</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
@media (max-width: 768px) {
  .header-center {
    flex-direction: row; /* Horizontal layout */
    justify-content: space-between;
  }
  
  .header .search-form {
    width: 65%; /* Further reduced width */
  }
  
  .header-center .icons {
    display: flex;
    align-items: center;
    gap: 1.5rem; /* Space between cart and menu */
  }
  
  .header-center #menu-btn {
    display: inline-block; /* Show menu button */
  }
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">3. Small Mobile Layout (450px):</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
@media (max-width: 450px) {
  .header .search-form {
    width: 60%; /* Most compact search width */
  }
  
  .header-center .icons {
    gap: 1rem; /* Reduced gap for small screens */
  }
  
  .header-center #menu-btn {
    font-size: 2.2rem; /* Slightly smaller icon */
  }
}
                    </pre>
                </div>
            </div>
        </div>
        
        <!-- Benefits -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🎯 Benefits Achieved</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">📏</div>
                    <h3>Better Space Utilization</h3>
                    <p>More efficient use of horizontal space on mobile and tablet devices with compact search bar</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">🎨</div>
                    <h3>Improved Visual Organization</h3>
                    <p>Cart and menu icons aligned on same line creates cleaner, more organized header layout</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Enhanced Mobile UX</h3>
                    <p>Optimized layouts for different screen sizes provide better user experience across all devices</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">⚙️</div>
                    <h3>Preserved Functionality</h3>
                    <p>All search, cart, and navigation features work exactly as before with improved layout efficiency</p>
                </div>
            </div>
        </div>
        
        <!-- Live Demo Instructions -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🧪 Live Demo</h2>
            <div class="responsive-instructions">
                <h4>Test the Responsive Header Improvements:</h4>
                <ul style="text-align: left; color: #856404; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li><strong>Desktop:</strong> Notice normal header layout with full navigation</li>
                    <li><strong>Tablet:</strong> Resize browser to ~900px width to see search bar reduction</li>
                    <li><strong>Mobile:</strong> Resize to ~700px to see cart and menu icons aligned</li>
                    <li><strong>Small Mobile:</strong> Resize to ~400px to see most compact layout</li>
                    <li><strong>Functionality:</strong> Test search, cart, and mobile menu at each breakpoint</li>
                </ul>
                
                <div style="text-align: center; margin: 2rem 0;">
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="index.html" class="btn" style="padding: 0.8rem 1.5rem; font-size: 1.2rem;">Homepage</a>
                        <a href="products.html" class="btn" style="padding: 0.8rem 1.5rem; font-size: 1.2rem;">Products Page</a>
                    </div>
                    <p style="color: #856404; margin-top: 1rem; font-size: 1.2rem;">Resize your browser window to test responsive breakpoints!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button class="close-cart" id="close-cart">&times;</button>
        </div>
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be added here -->
        </div>
        <div class="cart-total">
            <div class="total-price" id="total-price">Total: ₹0.00</div>
            <button class="btn checkout-btn">Checkout</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
</body>
</html>
