<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hover Effects & Image Alignment Demo - Fresh Picks</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        .demo-container {
            padding: 10rem 2rem 2rem;
            max-width: 120rem;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 4rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .demo-title {
            color: var(--green);
            font-size: 3rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .demo-subtitle {
            color: var(--black);
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--green);
            padding-bottom: 0.5rem;
        }
        
        .demo-info {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--green);
        }
        
        .improvement-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .improvement-item {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
            text-align: center;
        }
        
        .improvement-item h3 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .improvement-item p {
            color: #666;
            font-size: 1.4rem;
            line-height: 1.6;
        }
        
        .improvement-item .icon {
            font-size: 3rem;
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .before, .after {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
        }
        
        .before h3 {
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .after h3 {
            color: var(--green);
            margin-bottom: 1rem;
        }
        
        .demo-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 2rem 0;
            text-align: center;
        }
        
        .demo-instructions h4 {
            color: #856404;
            margin-bottom: 1rem;
            font-size: 1.6rem;
        }
        
        .demo-instructions p {
            color: #856404;
            font-size: 1.3rem;
            margin: 0.5rem 0;
        }
        
        .search-demo {
            max-width: 60rem;
            margin: 2rem auto;
            position: relative;
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
            
            .improvement-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header section with logo and navigation -->
    <header class="header">
        <div class="header-left">
            <a href="index.html" class="logo">
                <i class="fa fa-shopping-basket"></i> Fresh Picks
            </a>
        </div>
        
        <div class="header-center">
            <!-- Search form -->
            <div class="search-form">
                <form>
                    <input type="search" id="search-box" placeholder="Search products...">
                    <button type="submit"><i class="fa fa-search"></i></button>
                </form>
                <div class="search-results" id="search-results"></div>
            </div>
            
            <!-- Shopping cart icon with item count -->
            <div class="icons">
                <div id="cart-btn" class="fa fa-shopping-cart"><span class="cart-count">0</span></div>
            </div>
        </div>
        
        <div class="header-right">
            <!-- Mobile menu toggle -->
            <div class="icons">
                <div id="menu-btn" class="fa fa-bars"></div>
            </div>
            
            <!-- Navigation menu -->
            <nav class="navbar">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact Us</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="demo-container">
        <h1 class="demo-title">✨ Hover Effects & Image Alignment Improvements</h1>
        
        <!-- Overview Section -->
        <div class="demo-section">
            <h2 class="demo-subtitle">📋 Overview</h2>
            <div class="demo-info">
                <p><strong>Problems Solved:</strong></p>
                <ul style="text-align: left; color: #666; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li>Only product images had hover effects, not the entire product card</li>
                    <li>Product images were not properly centered within their containers</li>
                    <li>Inconsistent hover behavior across different product displays</li>
                    <li>Poor image alignment and sizing consistency</li>
                </ul>
                <p><strong>Solutions Implemented:</strong></p>
                <ul style="text-align: left; color: #666; font-size: 1.4rem; line-height: 1.8; margin: 1rem 0;">
                    <li>Entire product cards now have hover effects with smooth scaling</li>
                    <li>Perfect image centering using flexbox containers</li>
                    <li>Consistent hover behavior across all product displays</li>
                    <li>Touch device optimizations for better mobile experience</li>
                </ul>
            </div>
        </div>
        
        <!-- Key Improvements -->
        <div class="demo-section">
            <h2 class="demo-subtitle">✨ Key Improvements</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">🎯</div>
                    <h3>Expanded Hover Target</h3>
                    <p>Entire product card now responds to hover, not just the image. Better user experience and larger interaction area.</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📐</div>
                    <h3>Perfect Image Centering</h3>
                    <p>Images are now perfectly centered both horizontally and vertically within their containers using flexbox.</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">🔄</div>
                    <h3>Consistent Behavior</h3>
                    <p>Uniform hover effects across products page, search results, and all other product displays.</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Touch Device Optimized</h3>
                    <p>Smart hover detection prevents sticky hover states on touch devices while maintaining desktop experience.</p>
                </div>
            </div>
        </div>
        
        <!-- Before vs After -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🔄 Before vs After Comparison</h2>
            <div class="before-after">
                <div class="before">
                    <h3>❌ Before (Poor UX)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Only images had hover effects</li>
                        <li>Small hover target area</li>
                        <li>Images not properly centered</li>
                        <li>Inconsistent sizing and alignment</li>
                        <li>No touch device considerations</li>
                        <li>Poor visual feedback</li>
                    </ul>
                </div>
                <div class="after">
                    <h3>✅ After (Improved UX)</h3>
                    <ul style="text-align: left; color: #666; font-size: 1.3rem; line-height: 1.8;">
                        <li>Entire card responds to hover</li>
                        <li>Large, intuitive interaction area</li>
                        <li>Perfect image centering with flexbox</li>
                        <li>Consistent sizing across all displays</li>
                        <li>Touch device optimizations</li>
                        <li>Professional hover animations</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🧪 Live Demo</h2>
            <div class="demo-instructions">
                <h4>Try the Improved Hover Effects:</h4>
                <p>1. Hover over any product card below to see the entire card scale</p>
                <p>2. Notice how images are perfectly centered in their containers</p>
                <p>3. Try the search functionality to see consistent hover behavior</p>
                <p>4. Test on mobile devices to see touch optimizations</p>
            </div>
            
            <!-- Sample Product Grid -->
            <div class="product-grid" id="demo-product-grid" style="margin: 2rem 0;">
                <!-- Products will be loaded here -->
            </div>
            
            <!-- Search Demo -->
            <div class="search-demo">
                <div style="text-align: center; margin: 2rem 0;">
                    <p style="font-size: 1.4rem; color: #666; margin-bottom: 1rem;">
                        <strong>Test Search Hover Effects:</strong>
                    </p>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <button class="btn" onclick="document.getElementById('search-box').value='apple'; document.getElementById('search-box').dispatchEvent(new Event('input'));" style="padding: 0.5rem 1rem; font-size: 1.2rem;">Search Apple</button>
                        <button class="btn" onclick="document.getElementById('search-box').value='milk'; document.getElementById('search-box').dispatchEvent(new Event('input'));" style="padding: 0.5rem 1rem; font-size: 1.2rem;">Search Milk</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Implementation -->
        <div class="demo-section">
            <h2 class="demo-subtitle">⚙️ Technical Implementation</h2>
            <div class="demo-info">
                <h3 style="color: var(--green); margin-bottom: 1rem;">Key Changes Made:</h3>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">1. Hover Target Expansion:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
/* Before: Only image hover */
.product-grid .box img:hover {
    transform: scale(1.05);
}

/* After: Entire card hover */
.product-grid .box:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">2. Perfect Image Centering:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
.image-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 12rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
                    </pre>
                </div>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4 style="color: var(--black); margin-bottom: 1rem;">3. Touch Device Optimization:</h4>
                    <pre style="background: #fff; padding: 1rem; border-radius: 0.3rem; overflow-x: auto; font-size: 1.1rem; line-height: 1.5;">
@media (hover: none) and (pointer: coarse) {
    .product-grid .box:hover {
        transform: none; /* Disable scaling on touch */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
}
                    </pre>
                </div>
            </div>
        </div>
        
        <!-- Benefits -->
        <div class="demo-section">
            <h2 class="demo-subtitle">🎯 User Experience Benefits</h2>
            <div class="improvement-list">
                <div class="improvement-item">
                    <div class="icon">👆</div>
                    <h3>Better Interaction</h3>
                    <p>Larger hover target makes it easier for users to interact with product cards, especially on touch devices</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">🎨</div>
                    <h3>Professional Appearance</h3>
                    <p>Perfectly centered images and smooth animations create a polished, professional look</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">📱</div>
                    <h3>Mobile Optimized</h3>
                    <p>Smart touch detection prevents sticky hover states while maintaining desktop experience</p>
                </div>
                <div class="improvement-item">
                    <div class="icon">⚡</div>
                    <h3>Consistent Experience</h3>
                    <p>Uniform behavior across all product displays creates predictable user interactions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
            <button class="close-cart" id="close-cart">&times;</button>
        </div>
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be added here -->
        </div>
        <div class="cart-total">
            <div class="total-price" id="total-price">Total: ₹0.00</div>
            <button class="btn checkout-btn">Checkout</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Demo-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadDemoProducts();
        });
        
        function loadDemoProducts() {
            const demoContainer = document.getElementById('demo-product-grid');
            if (!demoContainer) return;
            
            // Show a selection of products to demonstrate the hover effects
            const demoProducts = products.slice(0, 6);
            
            demoContainer.innerHTML = '';
            demoProducts.forEach(product => {
                const productBox = document.createElement('div');
                productBox.className = 'box';
                productBox.innerHTML = `
                    ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
                    <div class="image-container">
                        <img src="${product.image}" alt="${product.name}" 
                             onerror="handleImageError(this, '${product.name}')"
                             onload="handleImageLoad(this)">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <p class="product-quantity">${extractQuantity(product.description)}</p>
                        <div class="product-price-section">
                            <div class="price-discount-row">
                                <div class="price">${formatPrice(product.price)}</div>
                                ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                            </div>
                            <div class="rating-inline">
                                ${generateStars(product.rating)} <span>(${product.rating})</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
                `;
                demoContainer.appendChild(productBox);
            });
        }
    </script>
</body>
</html>
