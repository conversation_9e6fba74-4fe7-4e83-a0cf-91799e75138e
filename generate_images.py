#!/usr/bin/env python3
"""
Simple script to generate placeholder images for the grocery store website.
This creates colored rectangles with text labels as placeholder images.
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_placeholder_image(width, height, text, bg_color, text_color, filename):
    """Create a placeholder image with text"""
    # Create image
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a better font, fall back to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
        except:
            font = ImageFont.load_default()
    
    # Get text size and position
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Save image
    img.save(filename)
    print(f"Created: {filename}")

def main():
    # Create image directory if it doesn't exist
    os.makedirs('image', exist_ok=True)
    
    # Image specifications
    images = [
        {'name': 'banner.jpg', 'text': 'Fresh Groceries', 'bg': '#27ae60', 'color': 'white', 'size': (800, 400)},
        {'name': 'category-1.jpg', 'text': 'Fresh Fruits', 'bg': '#e74c3c', 'color': 'white', 'size': (300, 200)},
        {'name': 'category-2.jpg', 'text': 'Vegetables', 'bg': '#2ecc71', 'color': 'white', 'size': (300, 200)},
        {'name': 'category-3.jpg', 'text': 'Dairy', 'bg': '#3498db', 'color': 'white', 'size': (300, 200)},
        {'name': 'category-4.jpg', 'text': 'Bakery', 'bg': '#f39c12', 'color': 'white', 'size': (300, 200)},
        {'name': 'apple.jpg', 'text': 'Apples', 'bg': '#e74c3c', 'color': 'white', 'size': (200, 150)},
        {'name': 'banana.jpg', 'text': 'Bananas', 'bg': '#f1c40f', 'color': 'black', 'size': (200, 150)},
        {'name': 'carrot.jpg', 'text': 'Carrots', 'bg': '#e67e22', 'color': 'white', 'size': (200, 150)},
        {'name': 'broccoli.jpg', 'text': 'Broccoli', 'bg': '#27ae60', 'color': 'white', 'size': (200, 150)},
        {'name': 'milk.jpg', 'text': 'Milk', 'bg': '#ecf0f1', 'color': 'black', 'size': (200, 150)},
        {'name': 'cheese.jpg', 'text': 'Cheese', 'bg': '#f39c12', 'color': 'white', 'size': (200, 150)},
        {'name': 'bread.jpg', 'text': 'Bread', 'bg': '#d35400', 'color': 'white', 'size': (200, 150)},
        {'name': 'croissant.jpg', 'text': 'Croissant', 'bg': '#e67e22', 'color': 'white', 'size': (200, 150)}
    ]
    
    # Generate images
    for img in images:
        filename = os.path.join('image', img['name'])
        create_placeholder_image(
            img['size'][0], img['size'][1],
            img['text'], img['bg'], img['color'],
            filename
        )
    
    print(f"\nGenerated {len(images)} placeholder images in the 'image' directory.")
    print("You can now test your grocery store website!")

if __name__ == "__main__":
    main()
