<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> <PERSON> Demo - Fresh Picks</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        
        .demo-info {
            background: #f8f9fa;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        
        .demo-info h1 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .demo-info p {
            color: #666;
            font-size: 1.4rem;
            max-width: 80rem;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
            gap: 2rem;
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature-item {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-item h3 {
            color: var(--green);
            margin-bottom: 0.5rem;
            font-size: 1.6rem;
        }
        
        .feature-item p {
            color: #666;
            font-size: 1.3rem;
            margin: 0;
        }
        
        .responsive-demo {
            background: #fff;
            padding: 2rem;
            text-align: center;
        }
        
        .responsive-demo h2 {
            color: var(--black);
            margin-bottom: 1rem;
        }
        
        .responsive-demo p {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .resize-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .resize-btn {
            padding: 0.8rem 1.5rem;
            background: var(--green);
            color: #fff;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1.2rem;
            transition: background 0.3s ease;
        }
        
        .resize-btn:hover {
            background: var(--dark-green);
        }
    </style>
</head>
<body>
    <!-- Demo Information -->
    <div class="demo-info">
        <h1>🎯 Fresh Picks Hero Banner Demo</h1>
        <p>
            This enhanced hero banner integrates seamlessly with the existing Fresh Picks brand identity, 
            featuring a modern 3-column layout that showcases Indian products with premium styling and 
            responsive design.
        </p>
        
        <div class="features-list">
            <div class="feature-item">
                <h3>🎨 Visual Design</h3>
                <p>Gradient background, glassmorphism effects, and smooth animations create a premium feel</p>
            </div>
            <div class="feature-item">
                <h3>📱 Responsive Layout</h3>
                <p>3-column grid on desktop, single column stack on mobile with optimized touch targets</p>
            </div>
            <div class="feature-item">
                <h3>🛒 Product Integration</h3>
                <p>Dynamic loading of Indian products with discounts, ratings, and cart functionality</p>
            </div>
            <div class="feature-item">
                <h3>💰 Currency Display</h3>
                <p>Proper Indian Rupee formatting with comma separators for amounts over ₹1,000</p>
            </div>
            <div class="feature-item">
                <h3>⚡ Performance</h3>
                <p>Optimized animations, lazy loading effects, and smooth hover interactions</p>
            </div>
            <div class="feature-item">
                <h3>🎯 Brand Alignment</h3>
                <p>Maintains Fresh Picks identity with green color scheme and Poppins typography</p>
            </div>
        </div>
    </div>
    
    <!-- Responsive Testing -->
    <div class="responsive-demo">
        <h2>📱 Responsive Design Testing</h2>
        <p>Use your browser's developer tools to test different screen sizes, or resize your browser window to see the responsive behavior.</p>
        
        <div class="resize-buttons">
            <button class="resize-btn" onclick="simulateDesktop()">🖥️ Desktop (1200px+)</button>
            <button class="resize-btn" onclick="simulateTablet()">📱 Tablet (768px)</button>
            <button class="resize-btn" onclick="simulateMobile()">📱 Mobile (375px)</button>
        </div>
    </div>

    <!-- Enhanced Hero Banner -->
    <section class="hero-new">
        <div class="hero-container">
            <div class="hero-column hero-left">
                <h3>Featured Products</h3>
                <div id="hero-featured-products">
                    <!-- Featured products will be loaded here -->
                </div>
            </div>
            <div class="hero-column hero-center">
                <h2>Fresh Groceries Delivered</h2>
                <p>Quality Indian products at your doorstep</p>
                <a href="products.html" class="btn btn-large">Shop Now</a>
            </div>
            <div class="hero-column hero-right">
                <h3>Best Sellers</h3>
                <div id="hero-bestsellers">
                    <!-- Best sellers will be loaded here -->
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Simulate different screen sizes for demo
        function simulateDesktop() {
            document.body.style.width = '100%';
            document.body.style.maxWidth = 'none';
            showNotification('Desktop view (1200px+) - Full 3-column layout');
        }
        
        function simulateTablet() {
            document.body.style.width = '768px';
            document.body.style.maxWidth = '768px';
            document.body.style.margin = '0 auto';
            showNotification('Tablet view (768px) - Responsive layout');
        }
        
        function simulateMobile() {
            document.body.style.width = '375px';
            document.body.style.maxWidth = '375px';
            document.body.style.margin = '0 auto';
            showNotification('Mobile view (375px) - Stacked layout');
        }
        
        // Add demo-specific enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add a demo badge
            const demoBadge = document.createElement('div');
            demoBadge.innerHTML = '🎯 DEMO';
            demoBadge.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #e74c3c;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: bold;
                z-index: 10000;
                font-size: 1.2rem;
                box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            `;
            document.body.appendChild(demoBadge);
            
            // Show welcome message
            setTimeout(() => {
                showNotification('🎉 Welcome to the Fresh Picks Hero Banner Demo! Scroll down to see the enhanced hero section.');
            }, 1000);
        });
    </script>
</body>
</html>
