<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Product Cards Demo - Fresh Picks</title>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CSS file -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    
    <style>
        .demo-header {
            background: var(--green);
            color: #fff;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .demo-header p {
            font-size: 1.6rem;
            opacity: 0.9;
            max-width: 80rem;
            margin: 0 auto;
        }
        
        .demo-section {
            padding: 3rem 2rem;
            background: #f8f9fa;
        }
        
        .demo-section h2 {
            text-align: center;
            color: var(--black);
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        
        .layout-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin: 3rem 0;
        }
        
        .comparison-section {
            background: #fff;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--box-shadow);
        }
        
        .comparison-section h3 {
            color: var(--green);
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            font-size: 1.4rem;
            color: #666;
        }
        
        .feature-list li::before {
            content: "✓ ";
            color: var(--green);
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .layout-comparison {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Demo Header -->
    <div class="demo-header">
        <h1>🎯 Simplified Product Cards</h1>
        <p>
            Clean, focused product card design emphasizing essential purchase information 
            while removing visual clutter for better user experience.
        </p>
    </div>
    
    <!-- Layout Comparison -->
    <div class="demo-section">
        <h2>Design Philosophy</h2>
        <div class="layout-comparison">
            <div class="comparison-section">
                <h3>❌ Previous Layout</h3>
                <ul class="feature-list">
                    <li>Long product descriptions</li>
                    <li>Detailed star ratings display</li>
                    <li>Multiple text elements</li>
                    <li>Visual clutter</li>
                    <li>Information overload</li>
                </ul>
            </div>
            <div class="comparison-section">
                <h3>✅ New Simplified Layout</h3>
                <ul class="feature-list">
                    <li>Product name only</li>
                    <li>Clear quantity/weight info</li>
                    <li>Price with inline rating</li>
                    <li>Centered Add to Cart button</li>
                    <li>Clean, focused design</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Product Cards Demo -->
    <section class="products" style="background: #fff;">
        <h1 class="heading">Simplified <span>Product Cards</span></h1>
        <div class="product-slider" id="demo-products">
            <!-- Products will be loaded here -->
        </div>
    </section>
    
    <!-- Search Demo -->
    <div class="demo-section">
        <h2>Search Results Demo</h2>
        <p style="text-align: center; margin-bottom: 2rem; font-size: 1.4rem; color: #666;">
            Try searching for products to see the simplified search results layout.
        </p>
        
        <div style="max-width: 50rem; margin: 0 auto;">
            <div class="search-form" style="position: relative;">
                <form>
                    <input type="search" id="demo-search-box" placeholder="Search products (try 'tomatoes' or 'paneer')...">
                    <button type="submit"><i class="fa fa-search"></i></button>
                </form>
                <div class="search-results" id="demo-search-results" style="position: absolute; top: 100%; left: 0; right: 0; z-index: 1000;"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Demo-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Load demo products
            loadDemoProducts();
            
            // Setup demo search
            setupDemoSearch();
        });
        
        function loadDemoProducts() {
            const demoContainer = document.getElementById('demo-products');
            if (!demoContainer) return;
            
            // Show a selection of products to demonstrate the layout
            const demoProducts = products.slice(0, 8);
            
            demoContainer.innerHTML = '';
            demoProducts.forEach(product => {
                const productBox = document.createElement('div');
                productBox.className = 'box';
                productBox.innerHTML = `
                    ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
                    <div class="image-container" style="position: relative;">
                        <img src="${product.image}" alt="${product.name}" 
                             onerror="handleImageError(this, '${product.name}')"
                             onload="handleImageLoad(this)">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">${product.name}</h3>
                        <p class="product-quantity">${extractQuantity(product.description)}</p>
                        <div class="product-price-section">
                            <div class="price-discount-row">
                                <div class="price">${formatPrice(product.price)}</div>
                                ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                            </div>
                            <div class="rating-inline">
                                ${generateStars(product.rating)} <span>(${product.rating})</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
                `;
                demoContainer.appendChild(productBox);
            });
        }
        
        function setupDemoSearch() {
            const demoSearchBox = document.getElementById('demo-search-box');
            const demoSearchResults = document.getElementById('demo-search-results');
            
            if (!demoSearchBox || !demoSearchResults) return;
            
            demoSearchBox.addEventListener('input', function() {
                const query = this.value.toLowerCase().trim();
                
                if (query.length === 0) {
                    demoSearchResults.innerHTML = '';
                    demoSearchResults.style.display = 'none';
                    return;
                }
                
                const filteredProducts = products.filter(product => 
                    product.name.toLowerCase().includes(query) ||
                    product.category.toLowerCase().includes(query) ||
                    product.description.toLowerCase().includes(query)
                );
                
                displayDemoSearchResults(filteredProducts);
            });
            
            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-form')) {
                    demoSearchResults.style.display = 'none';
                }
            });
        }
        
        function displayDemoSearchResults(results) {
            const demoSearchResults = document.getElementById('demo-search-results');
            demoSearchResults.innerHTML = '';
            
            if (results.length === 0) {
                demoSearchResults.innerHTML = '<div style="padding: 1rem; text-align: center; color: #666;">No products found</div>';
            } else {
                results.slice(0, 5).forEach(product => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'search-item';
                    resultItem.innerHTML = `
                        <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/50x50?text=No+Image'">
                        <div class="content">
                            <h4>${product.name}</h4>
                            <p class="search-quantity">${extractQuantity(product.description)}</p>
                            <div class="search-price-section">
                                <div class="search-price-discount-row">
                                    <div class="price">${formatPrice(product.price)}</div>
                                    ${product.discount ? `<div class="search-discount">${product.discount}</div>` : ''}
                                </div>
                                <div class="search-rating">
                                    ${generateStars(product.rating)} <span>(${product.rating})</span>
                                </div>
                            </div>
                        </div>
                    `;
                    resultItem.addEventListener('click', () => {
                        addToCart(product.id);
                        demoSearchBox.value = '';
                        demoSearchResults.style.display = 'none';
                    });
                    demoSearchResults.appendChild(resultItem);
                });
            }
            
            demoSearchResults.style.display = 'block';
        }
    </script>
</body>
</html>
