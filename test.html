<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fresh Picks - Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 2rem; }
        .test-section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; }
        .test-image { width: 200px; height: 150px; margin: 1rem; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Fresh Picks - Website Test Page</h1>
    
    <div class="test-section">
        <h2>Navigation Links Test</h2>
        <p>Click these links to test navigation:</p>
        <ul>
            <li><a href="index.html">Home Page</a></li>
            <li><a href="products.html">Products Page</a></li>
            <li><a href="about.html">About Page</a></li>
            <li><a href="contact.html">Contact Page</a></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Image Loading Test</h2>
        <p>These images should load if the image files were created correctly:</p>
        
        <div>
            <h3>Banner Image:</h3>
            <img src="image/banner.jpg" alt="Banner" class="test-image" 
                 onload="showStatus('banner-status', true)" 
                 onerror="showStatus('banner-status', false)">
            <span id="banner-status">Loading...</span>
        </div>
        
        <div>
            <h3>Category Images:</h3>
            <img src="image/category-1.jpg" alt="Fruits" class="test-image"
                 onload="showStatus('cat1-status', true)" 
                 onerror="showStatus('cat1-status', false)">
            <span id="cat1-status">Loading...</span>
            
            <img src="image/category-2.jpg" alt="Vegetables" class="test-image"
                 onload="showStatus('cat2-status', true)" 
                 onerror="showStatus('cat2-status', false)">
            <span id="cat2-status">Loading...</span>
        </div>
        
        <div>
            <h3>Product Images:</h3>
            <img src="image/apple.jpg" alt="Apple" class="test-image"
                 onload="showStatus('apple-status', true)" 
                 onerror="showStatus('apple-status', false)">
            <span id="apple-status">Loading...</span>
            
            <img src="image/banana.jpg" alt="Banana" class="test-image"
                 onload="showStatus('banana-status', true)" 
                 onerror="showStatus('banana-status', false)">
            <span id="banana-status">Loading...</span>
            
            <img src="image/milk.jpg" alt="Milk" class="test-image"
                 onload="showStatus('milk-status', true)" 
                 onerror="showStatus('milk-status', false)">
            <span id="milk-status">Loading...</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2>CSS and JavaScript Test</h2>
        <p>CSS file status: <span id="css-status">Checking...</span></p>
        <p>JavaScript file status: <span id="js-status">Checking...</span></p>
        <button onclick="testJavaScript()" style="padding: 0.5rem 1rem; margin: 1rem 0;">Test JavaScript Functions</button>
        <div id="js-test-results"></div>
    </div>
    
    <script>
        function showStatus(elementId, success) {
            const element = document.getElementById(elementId);
            if (success) {
                element.textContent = '✓ Loaded successfully';
                element.className = 'success';
            } else {
                element.textContent = '✗ Failed to load';
                element.className = 'error';
            }
        }
        
        function testJavaScript() {
            const results = document.getElementById('js-test-results');
            results.innerHTML = '<h4>JavaScript Test Results:</h4>';
            
            // Test if main script functions exist
            const tests = [
                { name: 'sampleProducts array', test: () => typeof sampleProducts !== 'undefined' },
                { name: 'addToCart function', test: () => typeof addToCart === 'function' },
                { name: 'updateCartUI function', test: () => typeof updateCartUI === 'function' },
                { name: 'handleSearch function', test: () => typeof handleSearch === 'function' }
            ];
            
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    results.innerHTML += `<p class="${passed ? 'success' : 'error'}">
                        ${passed ? '✓' : '✗'} ${test.name}: ${passed ? 'Available' : 'Not found'}
                    </p>`;
                } catch (e) {
                    results.innerHTML += `<p class="error">✗ ${test.name}: Error - ${e.message}</p>`;
                }
            });
        }
        
        // Check CSS loading
        window.addEventListener('load', function() {
            const cssStatus = document.getElementById('css-status');
            const testElement = document.createElement('div');
            testElement.style.display = 'none';
            testElement.className = 'btn'; // This class should be defined in CSS
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasCSS = computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
                          computedStyle.backgroundColor !== 'transparent';
            
            if (hasCSS) {
                cssStatus.textContent = '✓ CSS loaded successfully';
                cssStatus.className = 'success';
            } else {
                cssStatus.textContent = '✗ CSS not loaded properly';
                cssStatus.className = 'error';
            }
            
            document.body.removeChild(testElement);
        });
        
        // Check JavaScript loading
        window.addEventListener('load', function() {
            const jsStatus = document.getElementById('js-status');
            if (typeof sampleProducts !== 'undefined') {
                jsStatus.textContent = '✓ JavaScript loaded successfully';
                jsStatus.className = 'success';
            } else {
                jsStatus.textContent = '✗ JavaScript not loaded properly';
                jsStatus.className = 'error';
            }
        });
    </script>
    
    <!-- Load the main JavaScript file -->
    <script src="js/script.js"></script>
</body>
</html>
