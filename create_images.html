<!DOCTYPE html>
<html>
<head>
    <title>Generate Placeholder Images</title>
</head>
<body>
    <h1>Placeholder Image Generator</h1>
    <p>This page will help create placeholder images for the grocery store.</p>
    
    <canvas id="canvas" width="300" height="200" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Function to create a placeholder image
        function createPlaceholder(text, bgColor, textColor) {
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = textColor;
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, canvas.width/2, canvas.height/2);
            
            return canvas.toDataURL();
        }
        
        // Create and download images
        const images = [
            { name: 'banner.jpg', text: 'Fresh Groceries', bg: '#27ae60', color: 'white' },
            { name: 'category-1.jpg', text: 'Fresh Fruits', bg: '#e74c3c', color: 'white' },
            { name: 'category-2.jpg', text: 'Vegetables', bg: '#2ecc71', color: 'white' },
            { name: 'category-3.jpg', text: 'Dairy', bg: '#3498db', color: 'white' },
            { name: 'category-4.jpg', text: 'Bakery', bg: '#f39c12', color: 'white' },
            { name: 'apple.jpg', text: 'Apples', bg: '#e74c3c', color: 'white' },
            { name: 'banana.jpg', text: 'Bananas', bg: '#f1c40f', color: 'black' },
            { name: 'carrot.jpg', text: 'Carrots', bg: '#e67e22', color: 'white' },
            { name: 'broccoli.jpg', text: 'Broccoli', bg: '#27ae60', color: 'white' },
            { name: 'milk.jpg', text: 'Milk', bg: '#ecf0f1', color: 'black' },
            { name: 'cheese.jpg', text: 'Cheese', bg: '#f39c12', color: 'white' },
            { name: 'bread.jpg', text: 'Bread', bg: '#d35400', color: 'white' },
            { name: 'croissant.jpg', text: 'Croissant', bg: '#e67e22', color: 'white' }
        ];
        
        function downloadImage(dataUrl, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataUrl;
            link.click();
        }
        
        // Generate all images
        images.forEach((img, index) => {
            setTimeout(() => {
                const dataUrl = createPlaceholder(img.text, img.bg, img.color);
                downloadImage(dataUrl, img.name);
            }, index * 500);
        });
    </script>
</body>
</html>
