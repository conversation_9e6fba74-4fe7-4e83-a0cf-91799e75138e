/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Base styles and reset */
* {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
  border: none;
  text-decoration: none;
  text-transform: capitalize;
  transition: all .2s linear;
}

:root {
  --green: #27ae60;
  --dark-green: #219150;
  --black: #444;
  --light-color: #666;
  --border: .1rem solid rgba(0,0,0,.1);
  --border-hover: .1rem solid var(--black);
  --box-shadow: 0 .5rem 1rem rgba(0,0,0,.1);
}

html {
  font-size: 62.5%;
  overflow-x: hidden;
  scroll-behavior: smooth;
  scroll-padding-top: 7rem;
}

body {
  background: #f7f7f7;
}

section {
  padding: 2rem 9%;
}

.btn {
  display: inline-block;
  margin-top: 1rem;
  padding: .8rem 3rem;
  background: var(--green);
  color: #fff;
  font-size: 1.7rem;
  cursor: pointer;
  border-radius: .5rem;
}

.btn:hover {
  background: var(--dark-green);
}

.heading {
  text-align: center;
  padding: 2rem 0;
  padding-bottom: 3rem;
  font-size: 3.5rem;
  color: var(--black);
}

.heading span {
  background: var(--green);
  color: #fff;
  display: inline-block;
  padding: .5rem 3rem;
  clip-path: polygon(100% 0, 93% 50%, 100% 99%, 0% 100%, 7% 50%, 0% 0%);
}

/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 9%;
  background: #fff;
  box-shadow: var(--box-shadow);
}

.header .logo {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--black);
}

.header .logo i {
  color: var(--green);
  margin-right: .5rem;
}

.header .navbar ul {
  display: flex;
  list-style: none;
}

.header .navbar ul li {
  margin-left: 2rem;
}

.header .navbar ul li a {
  font-size: 1.7rem;
  color: var(--black);
}

.header .navbar ul li a:hover {
  color: var(--green);
}

.header .icons div {
  height: 4.5rem;
  width: 4.5rem;
  line-height: 4.5rem;
  border-radius: .5rem;
  background: #eee;
  color: var(--black);
  font-size: 2rem;
  margin-left: .3rem;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.header .icons div:hover {
  background: var(--green);
  color: #fff;
}

.header .icons .cart-count {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: var(--green);
  color: #fff;
  font-size: 1.2rem;
  height: 2rem;
  width: 2rem;
  line-height: 2rem;
  border-radius: 50%;
}

#menu-btn {
  display: none;
}

.header .search-form {
  width: 50rem;
  height: 5rem;
  border-radius: .5rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  background: #eee;
}

.header .search-form form {
  width: 100%;
  display: flex;
}

.header .search-form input {
  font-size: 1.6rem;
  padding: 0 1.2rem;
  height: 100%;
  width: 100%;
  background: none;
  color: var(--black);
}

.header .search-form button {
  font-size: 2.2rem;
  padding: 0 1.5rem;
  color: var(--black);
  background: #eee;
  cursor: pointer;
}

.header .search-form button:hover {
  color: var(--green);
}

/* Cart sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -100%;
  width: 35rem;
  height: 100vh;
  background: #fff;
  z-index: 1001;
  padding: 2rem;
  box-shadow: -2px 0 5px rgba(0,0,0,0.1);
  transition: right 0.3s ease;
}

.cart-sidebar.active {
  right: 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: var(--border);
}

.cart-header h3 {
  font-size: 2rem;
  color: var(--black);
}

#close-cart {
  font-size: 2rem;
  cursor: pointer;
  color: var(--black);
}

#close-cart:hover {
  color: var(--green);
}

.cart-items {
  max-height: calc(100vh - 15rem);
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 0;
  border-bottom: var(--border);
}

.cart-item img {
  height: 7rem;
  width: 7rem;
  object-fit: cover;
}

.cart-item-content {
  flex: 1;
}

.cart-item-content h3 {
  font-size: 1.7rem;
  color: var(--black);
}

.cart-item-content .price {
  font-size: 1.5rem;
  color: var(--green);
}

.cart-item-content .quantity {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.cart-item-content .quantity span {
  font-size: 1.4rem;
  color: var(--light-color);
}

.cart-item-content .quantity button {
  background: #eee;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.4rem;
}

.cart-item-content .quantity button:hover {
  background: var(--green);
  color: #fff;
}

.cart-item .remove-item {
  font-size: 1.7rem;
  cursor: pointer;
  color: #ff6b6b;
}

.cart-total {
  margin-top: 2rem;
  text-align: center;
}

.cart-total h3 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: 1rem;
}

/* Enhanced Hero Banner Section */
.hero-new {
  padding: 6rem 9% 4rem;
  background: linear-gradient(135deg, var(--green) 0%, var(--dark-green) 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
  min-height: 60vh;
}

.hero-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 3rem;
  max-width: 140rem;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero-column {
  padding: 2.5rem;
  position: relative;
}

.hero-left,
.hero-right {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1rem;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-left:hover,
.hero-right:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.hero-center {
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.hero-column h3 {
  font-size: 2.4rem;
  margin-bottom: 2rem;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  position: relative;
}

.hero-column h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: #fff;
  border-radius: 2px;
}

.hero-center h2 {
  font-size: 4.5rem;
  margin-bottom: 1.5rem;
  color: #fff;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -1px;
}

.hero-center p {
  font-size: 2rem;
  margin-bottom: 3rem;
  color: #fff;
  opacity: 0.95;
  font-weight: 300;
  max-width: 40rem;
  line-height: 1.4;
}

.btn-large {
  padding: 1.5rem 4.5rem;
  font-size: 1.8rem;
  background: #fff;
  color: var(--green);
  font-weight: 700;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.btn-large:hover::before {
  left: 100%;
}

.btn-large:hover {
  background: #f8f8f8;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Enhanced Hero Product Cards */
.hero-product-card {
  background: rgba(255, 255, 255, 0.98);
  color: var(--black);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 1rem;
  text-align: left;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.hero-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--green), var(--dark-green));
}

.hero-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  background: rgba(255, 255, 255, 1);
}

.hero-product-card img {
  width: 100%;
  height: 10rem;
  object-fit: cover;
  border-radius: 0.8rem;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.hero-product-card:hover img {
  transform: scale(1.05);
}

.hero-product-card h4 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--black);
  font-weight: 600;
  line-height: 1.3;
}

.hero-product-card .price {
  font-size: 1.6rem;
  color: var(--green);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.hero-product-card .discount {
  display: inline-block;
  background: #e74c3c;
  color: #fff;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-product-card .btn {
  padding: 0.8rem 1.5rem;
  font-size: 1.3rem;
  margin-top: 1rem;
  width: 100%;
  border-radius: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.hero-product-card .btn:hover {
  background: var(--dark-green);
  transform: translateY(-2px);
}

/* Hero section animations */
@keyframes heroFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heroSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes heroSlideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-center {
  animation: heroFadeIn 0.8s ease 0.2s both;
}

.hero-left {
  animation: heroSlideIn 0.8s ease 0.4s both;
}

.hero-right {
  animation: heroSlideInRight 0.8s ease 0.6s both;
}

/* Loading state for hero products */
.hero-products-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.4rem;
}

.hero-products-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  margin-left: 10px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced star ratings in hero cards */
.hero-product-card .stars i {
  color: #ffd700;
  font-size: 1.2rem;
  margin-right: 2px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Pulse effect for discount badges */
.hero-product-card .discount {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* Categories section */
.categories .box-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.categories .box-container .box {
  padding: 3rem 2rem;
  border-radius: .5rem;
  background: #fff;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.categories .box-container .box img {
  height: 15rem;
  width: 100%;
  object-fit: cover;
  border-radius: .5rem;
}

.categories .box-container .box h3 {
  font-size: 2rem;
  color: var(--black);
  padding: 1rem 0;
}

/* Products section */
.products .product-slider {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.products .product-slider .box {
  position: relative;
  background: #fff;
  border-radius: .5rem;
  text-align: center;
  padding: 3rem 2rem;
  box-shadow: var(--box-shadow);
}

.products .product-slider .box .discount {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 51, 153, .05);
  color: var(--green);
  padding: .7rem 1rem;
  font-size: 1.5rem;
  border-radius: .5rem;
  z-index: 1;
}

.products .product-slider .box img {
  height: 15rem;
  width: 100%;
  object-fit: contain;
}

.products .product-slider .box h3 {
  font-size: 2rem;
  color: var(--black);
  padding: .5rem 0;
}

.products .product-slider .box .price {
  font-size: 1.8rem;
  color: var(--green);
  padding: .5rem 0;
}

.products .product-slider .box .stars {
  padding: .5rem 0;
}

.products .product-slider .box .stars i {
  font-size: 1.7rem;
  color: gold;
}

/* Footer section */
.footer {
  background: #fff;
  padding-bottom: 9rem;
}

.footer .box-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.footer .box-container .box h3 {
  font-size: 2.2rem;
  color: var(--black);
  padding: 1rem 0;
}

.footer .box-container .box a {
  display: block;
  font-size: 1.4rem;
  color: var(--light-color);
  padding: 1rem 0;
}

.footer .box-container .box a i {
  color: var(--green);
  padding-right: .5rem;
}

.footer .box-container .box a:hover i {
  padding-right: 2rem;
}

.footer .credit {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  padding-top: 2.5rem;
  font-size: 2rem;
  color: var(--black);
  border-top: var(--border);
}

.footer .credit span {
  color: var(--green);
}

/* Media queries for responsiveness */
@media (max-width: 991px) {
  html {
    font-size: 55%;
  }
  
  .header {
    padding: 2rem;
  }
  
  section {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  #menu-btn {
    display: inline-block;
  }

  .header .navbar {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border-top: var(--border);
    border-bottom: var(--border);
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: .2s linear;
  }

  .header .navbar.active {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .header .navbar ul {
    flex-direction: column;
    padding: 2rem;
  }

  .header .navbar ul li {
    margin: 1rem 0;
  }

  .header .search-form {
    width: 90%;
    margin: 0 auto;
  }

  .hero-new {
    padding: 4rem 2rem 3rem;
    min-height: auto;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 100%;
  }

  .hero-center {
    order: 1;
  }

  .hero-left {
    order: 2;
  }

  .hero-right {
    order: 3;
  }

  .hero-center h2 {
    font-size: 3.5rem;
    line-height: 1.1;
  }

  .hero-center p {
    font-size: 1.7rem;
    margin-bottom: 2.5rem;
  }

  .hero-column {
    padding: 2rem;
  }

  .hero-column h3 {
    font-size: 2.2rem;
  }

  .btn-large {
    padding: 1.3rem 3.5rem;
    font-size: 1.6rem;
  }

  .hero-product-card {
    padding: 1.2rem;
    margin-bottom: 1.2rem;
  }

  .hero-product-card img {
    height: 8rem;
  }

  .hero-product-card h4 {
    font-size: 1.4rem;
  }

  .hero-product-card .price {
    font-size: 1.5rem;
  }
}

  .cart-sidebar {
    width: 30rem;
  }
}

@media (max-width: 450px) {
  html {
    font-size: 50%;
  }

  .header {
    padding: 2rem;
  }

  .header .search-form {
    width: 100%;
  }

  .hero {
    padding: 15rem 2rem;
  }

  .hero .content h3 {
    font-size: 2.5rem;
  }

  .categories .box-container {
    grid-template-columns: 1fr;
  }

  .products .product-slider {
    grid-template-columns: 1fr;
  }

  .cart-sidebar {
    width: 100%;
  }
}

/* Additional utility classes */
.hidden {
  display: none !important;
}

.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* Product card hover effects */
.products .product-slider .box:hover {
  transform: translateY(-0.5rem);
  box-shadow: 0 1rem 2rem rgba(0,0,0,.1);
}

/* Cart item animations */
.cart-item {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Search results styling */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: var(--border);
  border-top: none;
  max-height: 30rem;
  overflow-y: auto;
  z-index: 1000;
}

.search-results .search-item {
  padding: 1rem;
  border-bottom: var(--border);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-results .search-item:hover {
  background: #f8f8f8;
}

.search-results .search-item img {
  width: 5rem;
  height: 5rem;
  object-fit: cover;
  border-radius: 0.5rem;
}

.search-results .search-item .content h4 {
  font-size: 1.6rem;
  color: var(--black);
}

.search-results .search-item .content .price {
  font-size: 1.4rem;
  color: var(--green);
}

/* Products page specific styles */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 1.5rem;
}

.category-filters {
  margin-bottom: 3rem;
}

.filter-btn {
  margin: 0.5rem;
  background: #fff;
  color: var(--black);
  border: var(--border);
}

.filter-btn.active,
.filter-btn:hover {
  background: var(--green);
  color: #fff;
}

/* Active navigation link */
.navbar ul li a.active {
  color: var(--green);
}

/* About page styles */
.about-content {
  max-width: 80rem;
  margin: 0 auto;
  text-align: center;
}

.about-content h2 {
  font-size: 3rem;
  color: var(--black);
  margin-bottom: 2rem;
}

.about-content p {
  font-size: 1.6rem;
  color: var(--light-color);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.feature-box {
  background: #fff;
  padding: 3rem 2rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.feature-box i {
  font-size: 4rem;
  color: var(--green);
  margin-bottom: 1rem;
}

.feature-box h3 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: 1rem;
}

.feature-box p {
  font-size: 1.4rem;
  color: var(--light-color);
  line-height: 1.6;
}

/* Contact page styles */
.contact-form {
  max-width: 60rem;
  margin: 0 auto;
  background: #fff;
  padding: 3rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
}

.contact-form .form-group {
  margin-bottom: 2rem;
}

.contact-form label {
  display: block;
  font-size: 1.6rem;
  color: var(--black);
  margin-bottom: 0.5rem;
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 1rem;
  font-size: 1.6rem;
  border: var(--border);
  border-radius: 0.5rem;
  background: #f8f8f8;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: var(--green);
  background: #fff;
}

.contact-form textarea {
  height: 12rem;
  resize: vertical;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.contact-box {
  background: #fff;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
}

.contact-box i {
  font-size: 3rem;
  color: var(--green);
  margin-bottom: 1rem;
}

.contact-box h3 {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: 1rem;
}

.contact-box p {
  font-size: 1.4rem;
  color: var(--light-color);
}
