<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Loading Test - Fresh Picks</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-container {
            padding: 10rem 2rem 2rem;
            max-width: 120rem;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 4rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .test-title {
            color: var(--green);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .test-info {
            background: #fff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--green);
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 1.2rem;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .product-count {
            font-size: 1.8rem;
            color: var(--green);
            font-weight: bold;
            text-align: center;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 Fresh Picks Product Loading Test</h1>
        
        <!-- Test 1: Product Data Verification -->
        <div class="test-section">
            <h2>📊 Product Data Verification</h2>
            <div class="test-info">
                <p><strong>Purpose:</strong> Verify that product data is properly loaded and accessible</p>
            </div>
            <div id="product-data-test" class="debug-info">
                Running product data test...
            </div>
        </div>
        
        <!-- Test 2: Homepage Featured Products -->
        <div class="test-section">
            <h2>🏠 Homepage Featured Products Test</h2>
            <div class="test-info">
                <p><strong>Purpose:</strong> Test if featured products display correctly on homepage</p>
            </div>
            <div id="featured-products-test" class="debug-info">
                Testing featured products display...
            </div>
            <div class="product-slider" id="test-product-slider">
                <!-- Products will be loaded here -->
            </div>
        </div>
        
        <!-- Test 3: Products Page Grid -->
        <div class="test-section">
            <h2>📦 Products Page Grid Test</h2>
            <div class="test-info">
                <p><strong>Purpose:</strong> Test if product grid displays correctly on products page</p>
            </div>
            <div id="product-grid-test" class="debug-info">
                Testing product grid display...
            </div>
            <div class="product-grid" id="test-product-grid">
                <!-- Products will be loaded here -->
            </div>
        </div>
        
        <!-- Test 4: Hero Section Images -->
        <div class="test-section">
            <h2>🖼️ Hero Section Images Test</h2>
            <div class="test-info">
                <p><strong>Purpose:</strong> Test if hero section product images load correctly</p>
            </div>
            <div id="hero-images-test" class="debug-info">
                Testing hero section images...
            </div>
            <div class="hero-right" id="hero-product-images" style="margin: 2rem auto; max-width: 50rem;">
                <!-- Hero images will be loaded here -->
            </div>
        </div>
        
        <!-- Test 5: Search Functionality -->
        <div class="test-section">
            <h2>🔍 Search Functionality Test</h2>
            <div class="test-info">
                <p><strong>Purpose:</strong> Test if search functionality works with product data</p>
            </div>
            <div id="search-test" class="debug-info">
                Testing search functionality...
            </div>
            <div style="max-width: 50rem; margin: 2rem auto;">
                <div class="search-form">
                    <form>
                        <input type="search" id="test-search-box" placeholder="Search products...">
                        <button type="submit"><i class="fa fa-search"></i></button>
                    </form>
                    <div class="search-results" id="test-search-results"></div>
                </div>
            </div>
        </div>
        
        <!-- Test Results Summary -->
        <div class="test-section">
            <h2>📋 Test Results Summary</h2>
            <div id="test-summary" class="debug-info">
                Compiling test results...
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Test execution
        document.addEventListener('DOMContentLoaded', function() {
            runAllTests();
        });
        
        function runAllTests() {
            console.log('🧪 Starting Fresh Picks Product Loading Tests...');
            
            // Test 1: Product Data Verification
            testProductData();
            
            // Test 2: Featured Products Display
            testFeaturedProducts();
            
            // Test 3: Product Grid Display
            testProductGrid();
            
            // Test 4: Hero Images
            testHeroImages();
            
            // Test 5: Search Functionality
            testSearchFunctionality();
            
            // Generate summary
            setTimeout(generateTestSummary, 2000);
        }
        
        function testProductData() {
            const testDiv = document.getElementById('product-data-test');
            let results = [];
            
            try {
                // Check if products array exists
                if (typeof products === 'undefined') {
                    results.push('❌ ERROR: products array is undefined');
                    testDiv.innerHTML = results.join('<br>');
                    return;
                }
                
                results.push(`✅ SUCCESS: products array exists`);
                results.push(`📊 Product count: ${products.length}`);
                
                // Check product structure
                if (products.length > 0) {
                    const firstProduct = products[0];
                    const requiredFields = ['id', 'name', 'price', 'image', 'category', 'rating', 'description'];
                    
                    requiredFields.forEach(field => {
                        if (firstProduct.hasOwnProperty(field)) {
                            results.push(`✅ Field '${field}': present`);
                        } else {
                            results.push(`❌ Field '${field}': missing`);
                        }
                    });
                    
                    // Check categories
                    const categories = [...new Set(products.map(p => p.category))];
                    results.push(`📂 Categories found: ${categories.join(', ')}`);
                    
                    // Check price range
                    const prices = products.map(p => p.price);
                    const minPrice = Math.min(...prices);
                    const maxPrice = Math.max(...prices);
                    results.push(`💰 Price range: ₹${minPrice.toFixed(2)} - ₹${maxPrice.toFixed(2)}`);
                }
                
            } catch (error) {
                results.push(`❌ ERROR: ${error.message}`);
            }
            
            testDiv.innerHTML = results.join('<br>');
        }
        
        function testFeaturedProducts() {
            const testDiv = document.getElementById('featured-products-test');
            const sliderDiv = document.getElementById('test-product-slider');
            
            try {
                // Simulate featured products loading
                if (typeof displayProducts === 'function') {
                    // Temporarily override productSlider for testing
                    const originalSlider = window.productSlider;
                    window.productSlider = sliderDiv;
                    
                    displayProducts(products.slice(0, 6));
                    
                    // Restore original
                    window.productSlider = originalSlider;
                    
                    const loadedProducts = sliderDiv.children.length;
                    testDiv.innerHTML = `✅ SUCCESS: ${loadedProducts} featured products loaded successfully`;
                } else {
                    testDiv.innerHTML = '❌ ERROR: displayProducts function not found';
                }
            } catch (error) {
                testDiv.innerHTML = `❌ ERROR: ${error.message}`;
            }
        }
        
        function testProductGrid() {
            const testDiv = document.getElementById('product-grid-test');
            const gridDiv = document.getElementById('test-product-grid');
            
            try {
                // Simulate product grid loading
                gridDiv.innerHTML = '';
                
                products.slice(0, 8).forEach(product => {
                    const productBox = document.createElement('div');
                    productBox.className = 'box';
                    productBox.innerHTML = `
                        ${product.discount ? `<div class="discount">${product.discount}</div>` : ''}
                        <img src="${product.image}" alt="${product.name}" style="height: 12rem; width: 100%; object-fit: contain;">
                        <div class="product-info">
                            <h3 class="product-name">${product.name}</h3>
                            <p class="product-quantity">${extractQuantity(product.description)}</p>
                            <div class="product-price-section">
                                <div class="price-discount-row">
                                    <div class="price">${formatPrice(product.price)}</div>
                                    ${product.discount ? `<div class="discount-badge">${product.discount}</div>` : ''}
                                </div>
                                <div class="rating-inline">
                                    ${generateStars(product.rating)} <span>(${product.rating})</span>
                                </div>
                            </div>
                        </div>
                        <button class="btn add-to-cart-btn" onclick="addToCart(${product.id})">Add to Cart</button>
                    `;
                    gridDiv.appendChild(productBox);
                });
                
                const loadedProducts = gridDiv.children.length;
                testDiv.innerHTML = `✅ SUCCESS: ${loadedProducts} products loaded in grid successfully`;
            } catch (error) {
                testDiv.innerHTML = `❌ ERROR: ${error.message}`;
            }
        }
        
        function testHeroImages() {
            const testDiv = document.getElementById('hero-images-test');
            
            try {
                if (typeof loadHeroProducts === 'function') {
                    loadHeroProducts();
                    
                    setTimeout(() => {
                        const heroContainer = document.getElementById('hero-product-images');
                        const loadedImages = heroContainer.children.length;
                        testDiv.innerHTML = `✅ SUCCESS: ${loadedImages} hero images loaded successfully`;
                    }, 500);
                } else {
                    testDiv.innerHTML = '❌ ERROR: loadHeroProducts function not found';
                }
            } catch (error) {
                testDiv.innerHTML = `❌ ERROR: ${error.message}`;
            }
        }
        
        function testSearchFunctionality() {
            const testDiv = document.getElementById('search-test');
            const searchBox = document.getElementById('test-search-box');
            const searchResults = document.getElementById('test-search-results');
            
            try {
                // Test search with "apple"
                const query = 'apple';
                const filteredProducts = products.filter(product =>
                    product.name.toLowerCase().includes(query) ||
                    product.category.toLowerCase().includes(query) ||
                    product.description.toLowerCase().includes(query)
                );
                
                if (filteredProducts.length > 0) {
                    testDiv.innerHTML = `✅ SUCCESS: Search for "${query}" found ${filteredProducts.length} products`;
                    
                    // Display search results
                    searchResults.innerHTML = '';
                    filteredProducts.slice(0, 3).forEach(product => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'search-item';
                        resultItem.innerHTML = `
                            <img src="${product.image}" alt="${product.name}" style="width: 50px; height: 50px; object-fit: contain;">
                            <div class="content">
                                <h4>${product.name}</h4>
                                <div class="price">${formatPrice(product.price)}</div>
                            </div>
                        `;
                        searchResults.appendChild(resultItem);
                    });
                    searchResults.style.display = 'block';
                } else {
                    testDiv.innerHTML = `❌ ERROR: Search for "${query}" found no products`;
                }
            } catch (error) {
                testDiv.innerHTML = `❌ ERROR: ${error.message}`;
            }
        }
        
        function generateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            const testDivs = [
                'product-data-test',
                'featured-products-test', 
                'product-grid-test',
                'hero-images-test',
                'search-test'
            ];
            
            let passedTests = 0;
            let totalTests = testDivs.length;
            
            testDivs.forEach(divId => {
                const div = document.getElementById(divId);
                if (div.innerHTML.includes('✅ SUCCESS')) {
                    passedTests++;
                }
            });
            
            const successRate = (passedTests / totalTests * 100).toFixed(1);
            
            let summary = `
                <strong>📊 Test Results Summary:</strong><br>
                ✅ Passed: ${passedTests}/${totalTests} tests<br>
                📈 Success Rate: ${successRate}%<br><br>
            `;
            
            if (passedTests === totalTests) {
                summary += '<span class="success">🎉 ALL TESTS PASSED! Products are loading correctly.</span>';
            } else {
                summary += '<span class="error">⚠️ Some tests failed. Check individual test results above.</span>';
            }
            
            summaryDiv.innerHTML = summary;
        }
    </script>
</body>
</html>
